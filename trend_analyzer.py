"""
双色球趋势分析模块
提供详细的号码趋势分析功能
"""

import json
import os
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from typing import List, Dict, Tuple
import pandas as pd
from data_processor import DataProcessor


class TrendAnalyzer:
    """趋势分析器"""
    
    def __init__(self):
        self.processor = DataProcessor()
        self.data = []
        
    def load_data(self) -> bool:
        """加载历史数据"""
        self.data = self.processor.load_from_json()
        if not self.data:
            print("❌ 没有找到历史数据")
            return False
        
        print(f"✅ 成功加载 {len(self.data)} 期历史数据")
        return True
    
    def analyze_number_trends(self, periods: int = 50) -> Dict:
        """分析号码趋势"""
        if not self.data:
            return {}
        
        recent_data = self.data[:periods]
        
        # 红球趋势分析
        red_trends = self._analyze_red_ball_trends(recent_data)
        
        # 蓝球趋势分析
        blue_trends = self._analyze_blue_ball_trends(recent_data)
        
        # 遗漏分析
        missing_analysis = self._analyze_missing_numbers()
        
        # 出现频率趋势
        frequency_trends = self._analyze_frequency_trends(periods)
        
        return {
            'analysis_periods': periods,
            'red_trends': red_trends,
            'blue_trends': blue_trends,
            'missing_analysis': missing_analysis,
            'frequency_trends': frequency_trends,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _analyze_red_ball_trends(self, recent_data: List[Dict]) -> Dict:
        """分析红球趋势"""
        red_counter = Counter()
        red_positions = defaultdict(list)  # 记录每个号码在每期中的位置
        
        for period_idx, item in enumerate(recent_data):
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            red_balls_sorted = sorted(red_balls_int)
            
            for pos, ball in enumerate(red_balls_sorted):
                red_counter[ball] += 1
                red_positions[ball].append(pos)
        
        # 计算趋势指标
        total_periods = len(recent_data)
        red_trends = {}
        
        for ball in range(1, 34):
            count = red_counter.get(ball, 0)
            frequency = count / total_periods if total_periods > 0 else 0
            
            # 计算趋势方向（最近10期 vs 之前期数）
            recent_10_count = 0
            for item in recent_data[:10]:
                red_balls = item.get('red_balls', [])
                if isinstance(red_balls, str):
                    red_balls = red_balls.split(',')
                red_balls_int = [int(b) for b in red_balls if b.isdigit()]
                if ball in red_balls_int:
                    recent_10_count += 1
            
            earlier_count = count - recent_10_count
            earlier_periods = total_periods - 10
            
            recent_freq = recent_10_count / 10 if 10 > 0 else 0
            earlier_freq = earlier_count / earlier_periods if earlier_periods > 0 else 0
            
            trend_direction = "上升" if recent_freq > earlier_freq else "下降" if recent_freq < earlier_freq else "平稳"
            
            # 计算热度等级
            if frequency >= 0.25:  # 出现频率超过25%
                heat_level = "极热"
            elif frequency >= 0.15:
                heat_level = "热门"
            elif frequency >= 0.08:
                heat_level = "温热"
            elif frequency >= 0.04:
                heat_level = "一般"
            else:
                heat_level = "冷门"
            
            red_trends[ball] = {
                'count': count,
                'frequency': frequency,
                'percentage': frequency * 100,
                'trend_direction': trend_direction,
                'heat_level': heat_level,
                'recent_10_count': recent_10_count,
                'positions': red_positions.get(ball, [])
            }
        
        # 排序
        hot_numbers = sorted(red_trends.items(), key=lambda x: x[1]['count'], reverse=True)[:10]
        cold_numbers = sorted(red_trends.items(), key=lambda x: x[1]['count'])[:10]
        rising_numbers = [item for item in red_trends.items() if item[1]['trend_direction'] == "上升"][:8]
        falling_numbers = [item for item in red_trends.items() if item[1]['trend_direction'] == "下降"][:8]
        
        return {
            'all_trends': red_trends,
            'hot_numbers': [(ball, data) for ball, data in hot_numbers],
            'cold_numbers': [(ball, data) for ball, data in cold_numbers],
            'rising_numbers': [(ball, data) for ball, data in rising_numbers],
            'falling_numbers': [(ball, data) for ball, data in falling_numbers]
        }
    
    def _analyze_blue_ball_trends(self, recent_data: List[Dict]) -> Dict:
        """分析蓝球趋势"""
        blue_counter = Counter()
        
        for item in recent_data:
            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                blue_counter[int(blue_ball)] += 1
        
        total_periods = len(recent_data)
        blue_trends = {}
        
        for ball in range(1, 17):
            count = blue_counter.get(ball, 0)
            frequency = count / total_periods if total_periods > 0 else 0
            
            # 计算趋势方向
            recent_5_count = 0
            for item in recent_data[:5]:
                blue_ball = item.get('blue_ball', '')
                if blue_ball and str(blue_ball).isdigit() and int(blue_ball) == ball:
                    recent_5_count += 1
            
            earlier_count = count - recent_5_count
            earlier_periods = total_periods - 5
            
            recent_freq = recent_5_count / 5 if 5 > 0 else 0
            earlier_freq = earlier_count / earlier_periods if earlier_periods > 0 else 0
            
            trend_direction = "上升" if recent_freq > earlier_freq else "下降" if recent_freq < earlier_freq else "平稳"
            
            # 热度等级
            if frequency >= 0.15:
                heat_level = "极热"
            elif frequency >= 0.10:
                heat_level = "热门"
            elif frequency >= 0.06:
                heat_level = "温热"
            else:
                heat_level = "冷门"
            
            blue_trends[ball] = {
                'count': count,
                'frequency': frequency,
                'percentage': frequency * 100,
                'trend_direction': trend_direction,
                'heat_level': heat_level,
                'recent_5_count': recent_5_count
            }
        
        # 排序
        hot_blues = sorted(blue_trends.items(), key=lambda x: x[1]['count'], reverse=True)[:8]
        cold_blues = sorted(blue_trends.items(), key=lambda x: x[1]['count'])[:8]
        
        return {
            'all_trends': blue_trends,
            'hot_numbers': [(ball, data) for ball, data in hot_blues],
            'cold_numbers': [(ball, data) for ball, data in cold_blues]
        }
    
    def _analyze_missing_numbers(self) -> Dict:
        """分析遗漏号码"""
        red_missing = {i: 0 for i in range(1, 34)}
        blue_missing = {i: 0 for i in range(1, 17)}
        
        # 分析红球遗漏
        for period_idx, item in enumerate(self.data):
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            
            # 更新遗漏计数
            for ball in range(1, 34):
                if ball in red_balls_int:
                    red_missing[ball] = 0  # 重置遗漏
                else:
                    red_missing[ball] += 1
        
        # 分析蓝球遗漏
        for period_idx, item in enumerate(self.data):
            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                blue_ball_int = int(blue_ball)
                
                for ball in range(1, 17):
                    if ball == blue_ball_int:
                        blue_missing[ball] = 0
                    else:
                        blue_missing[ball] += 1
        
        # 分类遗漏程度
        def classify_missing(missing_periods):
            if missing_periods >= 50:
                return "超长遗漏"
            elif missing_periods >= 30:
                return "长期遗漏"
            elif missing_periods >= 15:
                return "中期遗漏"
            elif missing_periods >= 5:
                return "短期遗漏"
            else:
                return "近期出现"
        
        red_missing_classified = {}
        for ball, periods in red_missing.items():
            red_missing_classified[ball] = {
                'missing_periods': periods,
                'classification': classify_missing(periods)
            }
        
        blue_missing_classified = {}
        for ball, periods in blue_missing.items():
            blue_missing_classified[ball] = {
                'missing_periods': periods,
                'classification': classify_missing(periods)
            }
        
        return {
            'red_missing': red_missing_classified,
            'blue_missing': blue_missing_classified,
            'red_most_missing': sorted(red_missing.items(), key=lambda x: x[1], reverse=True)[:10],
            'blue_most_missing': sorted(blue_missing.items(), key=lambda x: x[1], reverse=True)[:8]
        }
    
    def _analyze_frequency_trends(self, periods: int) -> Dict:
        """分析频率趋势变化"""
        # 分析不同时间段的频率变化
        segments = [10, 20, 30, periods]  # 不同时间段
        frequency_changes = {}
        
        for segment in segments:
            if segment > len(self.data):
                continue
                
            segment_data = self.data[:segment]
            red_counter = Counter()
            blue_counter = Counter()
            
            for item in segment_data:
                red_balls = item.get('red_balls', [])
                if isinstance(red_balls, str):
                    red_balls = red_balls.split(',')
                
                for ball in red_balls:
                    if ball.isdigit():
                        red_counter[int(ball)] += 1
                
                blue_ball = item.get('blue_ball', '')
                if blue_ball and str(blue_ball).isdigit():
                    blue_counter[int(blue_ball)] += 1
            
            frequency_changes[f'last_{segment}_periods'] = {
                'red_top5': red_counter.most_common(5),
                'blue_top3': blue_counter.most_common(3)
            }
        
        return frequency_changes
    
    def generate_trend_report(self, periods: int = 50) -> str:
        """生成趋势分析报告"""
        if not self.load_data():
            return "数据加载失败"
        
        trends = self.analyze_number_trends(periods)
        
        report = f"""
双色球趋势分析报告
{'='*60}
分析期数: 最近 {periods} 期
分析时间: {trends['analysis_time']}
数据来源: {len(self.data)} 期历史数据

红球趋势分析
{'='*40}
🔥 热门红球 (最近{periods}期):
"""
        
        for ball, data in trends['red_trends']['hot_numbers'][:8]:
            report += f"   {ball:2d}号: 出现{data['count']:2d}次 ({data['percentage']:.1f}%) - {data['heat_level']} - 趋势{data['trend_direction']}\n"
        
        report += f"\n❄️ 冷门红球 (最近{periods}期):\n"
        for ball, data in trends['red_trends']['cold_numbers'][:8]:
            report += f"   {ball:2d}号: 出现{data['count']:2d}次 ({data['percentage']:.1f}%) - {data['heat_level']} - 趋势{data['trend_direction']}\n"
        
        report += f"\n📈 上升趋势红球:\n"
        for ball, data in trends['red_trends']['rising_numbers'][:6]:
            report += f"   {ball:2d}号: 最近10期{data['recent_10_count']}次 - {data['trend_direction']}\n"
        
        report += f"\n📉 下降趋势红球:\n"
        for ball, data in trends['red_trends']['falling_numbers'][:6]:
            report += f"   {ball:2d}号: 最近10期{data['recent_10_count']}次 - {data['trend_direction']}\n"
        
        report += f"\n蓝球趋势分析\n{'='*40}\n"
        report += f"🔥 热门蓝球 (最近{periods}期):\n"
        for ball, data in trends['blue_trends']['hot_numbers'][:5]:
            report += f"   {ball:2d}号: 出现{data['count']:2d}次 ({data['percentage']:.1f}%) - {data['heat_level']} - 趋势{data['trend_direction']}\n"
        
        report += f"\n❄️ 冷门蓝球 (最近{periods}期):\n"
        for ball, data in trends['blue_trends']['cold_numbers'][:5]:
            report += f"   {ball:2d}号: 出现{data['count']:2d}次 ({data['percentage']:.1f}%) - {data['heat_level']} - 趋势{data['trend_direction']}\n"
        
        report += f"\n遗漏分析\n{'='*40}\n"
        report += f"🔴 红球遗漏最久:\n"
        for ball, periods in trends['missing_analysis']['red_most_missing'][:8]:
            classification = trends['missing_analysis']['red_missing'][ball]['classification']
            report += f"   {ball:2d}号: 遗漏{periods:2d}期 - {classification}\n"
        
        report += f"\n🔵 蓝球遗漏最久:\n"
        for ball, periods in trends['missing_analysis']['blue_most_missing'][:5]:
            classification = trends['missing_analysis']['blue_missing'][ball]['classification']
            report += f"   {ball:2d}号: 遗漏{periods:2d}期 - {classification}\n"
        
        report += f"\n{'='*60}\n"
        report += "注意: 趋势分析仅供参考，彩票开奖具有随机性\n"
        
        return report
    
    def save_trend_analysis(self, periods: int = 50):
        """保存趋势分析结果"""
        if not self.load_data():
            return
        
        trends = self.analyze_number_trends(periods)
        report = self.generate_trend_report(periods)
        
        # 保存JSON数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = f"data/trend_analysis_{timestamp}.json"
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(trends, f, ensure_ascii=False, indent=2)
        
        # 保存文本报告
        txt_file = f"data/trend_report_{timestamp}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 趋势分析已保存:")
        print(f"   JSON数据: {json_file}")
        print(f"   文本报告: {txt_file}")


def main():
    """主函数"""
    analyzer = TrendAnalyzer()
    
    print("🎯 双色球趋势分析工具")
    print("=" * 50)
    
    # 生成并显示趋势报告
    report = analyzer.generate_trend_report(50)
    print(report)
    
    # 保存分析结果
    analyzer.save_trend_analysis(50)


if __name__ == "__main__":
    main()
