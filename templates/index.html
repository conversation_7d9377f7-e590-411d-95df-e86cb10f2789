<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双色球历史数据展示系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .red-ball {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
        }
        .blue-ball {
            background: #0d6efd;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
        }
        .loading-spinner {
            display: none;
        }
        .data-card {
            transition: transform 0.2s;
        }
        .data-card:hover {
            transform: translateY(-2px);
        }
        .lottery-table {
            font-size: 11px;
        }
        .lottery-table th {
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
        }
        .lottery-table td {
            padding: 2px;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }
        .zone1-header {
            background-color: #ffebee !important;
            color: #d32f2f;
        }
        .zone2-header {
            background-color: #e8f5e8 !important;
            color: #388e3c;
        }
        .zone3-header {
            background-color: #fff3e0 !important;
            color: #f57c00;
        }
        .zone1-hit {
            background-color: #ffcdd2 !important;
            color: #d32f2f !important;
            font-weight: bold;
        }
        .zone1-miss {
            background-color: #ffebee !important;
            color: #ccc !important;
        }
        .zone2-hit {
            background-color: #c8e6c9 !important;
            color: #388e3c !important;
            font-weight: bold;
        }
        .zone2-miss {
            background-color: #e8f5e8 !important;
            color: #ccc !important;
        }
        .zone3-hit {
            background-color: #ffe0b2 !important;
            color: #f57c00 !important;
            font-weight: bold;
        }
        .zone3-miss {
            background-color: #fff3e0 !important;
            color: #ccc !important;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i> 双色球历史数据展示系统
            </a>
            <button class="btn btn-outline-light" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 加载状态 -->
        <div id="loading-section" class="text-center mb-4">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">
                    <div class="progress" style="width: 300px; margin: 0 auto;">
                        <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small id="loading-message" class="text-muted">正在加载数据...</small>
                </div>
            </div>
        </div>

        <!-- 数据概要 -->
        <div id="summary-section" class="row mb-4" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> 数据概要</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="total-periods" class="text-primary">-</h4>
                                    <small class="text-muted">总期数</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="latest-period" class="text-success">-</h4>
                                    <small class="text-muted">最新期号</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="earliest-period" class="text-info">-</h4>
                                    <small class="text-muted">最早期号</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6 id="latest-date" class="text-dark">-</h6>
                                    <small class="text-muted">最新开奖日期</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6 id="earliest-date" class="text-dark">-</h6>
                                    <small class="text-muted">最早开奖日期</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能选项卡 -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent" type="button">
                    <i class="bi bi-clock-history"></i> 最近开奖
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button">
                    <i class="bi bi-search"></i> 数据查询
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button">
                    <i class="bi bi-bar-chart"></i> 统计分析
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trend-tab" data-bs-toggle="tab" data-bs-target="#trend" type="button">
                    <i class="bi bi-graph-up-arrow"></i> 趋势分析
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="prediction-tab" data-bs-toggle="tab" data-bs-target="#prediction" type="button">
                    <i class="bi bi-magic"></i> 智能预测
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabsContent">
            <!-- 最近开奖 -->
            <div class="tab-pane fade show active" id="recent" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-clock-history"></i> 最近开奖记录</h5>
                        <select id="recent-limit" class="form-select" style="width: auto;" onchange="loadRecentData()">
                            <option value="20">最近20期</option>
                            <option value="50" selected>最近50期</option>
                            <option value="100">最近100期</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div id="recent-data" class="row">
                            <!-- 数据将通过JavaScript加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据查询 -->
            <div class="tab-pane fade" id="search" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="bi bi-search"></i> 数据查询</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">期号搜索</label>
                                <input type="text" id="search-period" class="form-control" placeholder="输入期号">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">年份筛选</label>
                                <select id="search-year" class="form-select">
                                    <option value="">全部年份</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">每页显示</label>
                                <select id="search-per-page" class="form-select">
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block" onclick="searchData()">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                        <div id="search-results">
                            <!-- 搜索结果将显示在这里 -->
                        </div>
                        <div id="search-pagination" class="d-flex justify-content-center mt-3">
                            <!-- 分页将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计分析 -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart"></i> 红球出现频率 TOP15</h5>
                            </div>
                            <div class="card-body">
                                <div id="red-frequency-chart">
                                    <!-- 红球频率图表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart"></i> 蓝球出现频率 TOP10</h5>
                            </div>
                            <div class="card-body">
                                <div id="blue-frequency-chart">
                                    <!-- 蓝球频率图表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="bi bi-thermometer-half"></i> 号码冷热趋势</h5>
                                <select id="trend-periods" class="form-select" style="width: auto;" onchange="loadTrends()">
                                    <option value="20">最近20期</option>
                                    <option value="30" selected>最近30期</option>
                                    <option value="50">最近50期</option>
                                    <option value="100">最近100期</option>
                                </select>
                            </div>
                            <div class="card-body">
                                <div id="trends-analysis">
                                    <!-- 趋势分析将显示在这里 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势分析 -->
            <div class="tab-pane fade" id="trend" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-graph-up-arrow"></i> 趋势分析</h5>
                        <div class="d-flex gap-2">
                            <select id="trend-analysis-periods" class="form-select" style="width: auto;">
                                <option value="30">最近30期</option>
                                <option value="50" selected>最近50期</option>
                                <option value="100">最近100期</option>
                            </select>
                            <button class="btn btn-primary" onclick="loadTrendAnalysis()">
                                <i class="bi bi-arrow-clockwise"></i> 分析趋势
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="trend-analysis-results">
                            <div class="text-center text-muted">
                                <i class="bi bi-graph-up-arrow" style="font-size: 3rem;"></i>
                                <p>点击"分析趋势"按钮开始趋势分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能预测 -->
            <div class="tab-pane fade" id="prediction" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-magic"></i> 智能预测</h5>
                        <button class="btn btn-success" onclick="generatePrediction()">
                            <i class="bi bi-arrow-clockwise"></i> 生成新预测
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="prediction-results">
                            <div class="text-center text-muted">
                                <i class="bi bi-magic" style="font-size: 3rem;"></i>
                                <p>点击"生成新预测"按钮开始预测分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>