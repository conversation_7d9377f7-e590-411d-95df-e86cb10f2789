"""
完整双色球历史数据获取器
从2003年双色球开始发行至今的所有开奖数据
"""

import requests
import json
import time
import os
from datetime import datetime, date
from typing import List, Dict, Optional
import config
from official_api_crawler import OfficialAPISSQCrawler


class CompleteHistoryCrawler:
    """完整历史数据爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.DEFAULT_HEADERS)
        
        # 确保数据目录存在
        self.data_dir = config.DATA_DIR
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def get_complete_history(self) -> List[Dict]:
        """获取完整历史数据"""
        print("🎯 开始获取双色球完整历史数据（2003年至今）...")
        
        # 首先尝试从官方API获取所有可用数据
        print("📡 正在从官方API获取数据...")
        api_crawler = OfficialAPISSQCrawler()
        api_data = api_crawler.get_historical_data()
        
        print(f"✅ 从官方API获取到 {len(api_data)} 期数据")
        
        # 检查最早的数据是否到达2003年
        if api_data:
            earliest_period = api_data[-1]['period']
            earliest_year = int(earliest_period[:4])
            print(f"📅 官方API最早数据：{earliest_period} ({earliest_year}年)")
            
            if earliest_year <= 2003:
                print("🎉 官方API已包含2003年以来的完整数据！")
                return self._sort_and_validate_data(api_data)
            else:
                print(f"⚠️ 官方API数据从{earliest_year}年开始，缺少2003-{earliest_year-1}年的数据")
                print("🔍 尝试获取更早期的数据...")
                
                # 尝试获取更早期的数据
                early_data = self._get_early_period_data(2003, earliest_year - 1)
                
                if early_data:
                    print(f"✅ 获取到早期数据 {len(early_data)} 期")
                    all_data = early_data + api_data
                    return self._sort_and_validate_data(all_data)
                else:
                    print("❌ 无法获取早期数据，返回现有数据")
                    return self._sort_and_validate_data(api_data)
        else:
            print("❌ 官方API未返回任何数据")
            return []
    
    def _get_early_period_data(self, start_year: int, end_year: int) -> List[Dict]:
        """尝试获取早期数据"""
        print(f"🔍 尝试获取 {start_year}-{end_year} 年的早期数据...")
        
        early_data = []
        
        # 尝试不同的API参数来获取早期数据
        for year in range(start_year, end_year + 1):
            print(f"📅 尝试获取 {year} 年数据...")
            
            # 尝试按年份查询
            year_data = self._get_year_data(year)
            if year_data:
                early_data.extend(year_data)
                print(f"✅ 获取到 {year} 年数据 {len(year_data)} 期")
            else:
                print(f"❌ 未能获取到 {year} 年数据")
            
            time.sleep(1)  # 避免请求过快
        
        return early_data
    
    def _get_year_data(self, year: int) -> List[Dict]:
        """获取指定年份的数据"""
        try:
            # 尝试使用日期范围查询
            params = {
                'name': 'ssq',
                'issueCount': '',
                'issueStart': f'{year}001',
                'issueEnd': f'{year}156',
                'dayStart': f'{year}-01-01',
                'dayEnd': f'{year}-12-31',
                'pageNo': 1,
                'pageSize': 200,  # 增大页面大小
                'systemType': 'PC'
            }
            
            response = self.session.get(
                config.OFFICIAL_API_URL,
                params=params,
                timeout=config.REQUEST_TIMEOUT
            )
            response.raise_for_status()
            
            if 'json' in response.headers.get('content-type', '').lower():
                data = response.json()
                
                # 解析响应数据
                if 'result' in data and isinstance(data['result'], list):
                    year_data = []
                    for item in data['result']:
                        parsed_item = self._parse_lottery_data(item)
                        if parsed_item:
                            year_data.append(parsed_item)
                    return year_data
            
        except Exception as e:
            print(f"❌ 获取 {year} 年数据失败: {e}")
        
        return []
    
    def _parse_lottery_data(self, item: Dict) -> Optional[Dict]:
        """解析单条开奖数据"""
        try:
            # 提取基本信息
            period = item.get('code', '')
            draw_date = item.get('date', '')
            
            # 提取开奖号码
            red_balls = []
            blue_ball = ''
            
            # 尝试从不同字段获取号码
            if 'red' in item:
                red_balls = item['red'].split(',') if isinstance(item['red'], str) else item['red']
            elif 'reds' in item:
                red_balls = item['reds'].split(',') if isinstance(item['reds'], str) else item['reds']
            
            if 'blue' in item:
                blue_ball = str(item['blue'])
            elif 'blues' in item:
                blue_ball = str(item['blues'])
            
            # 验证数据完整性
            if not period or not red_balls or not blue_ball:
                return None
            
            # 格式化号码
            red_balls_formatted = [f"{int(ball):02d}" for ball in red_balls if str(ball).isdigit()]
            blue_ball_formatted = f"{int(blue_ball):02d}" if str(blue_ball).isdigit() else blue_ball
            
            if len(red_balls_formatted) != 6:
                return None
            
            return {
                'period': period,
                'draw_date': draw_date,
                'red_balls': red_balls_formatted,
                'blue_ball': blue_ball_formatted,
                'red_balls_str': ','.join(red_balls_formatted),
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'complete_history_api'
            }
            
        except Exception as e:
            print(f"❌ 解析数据失败: {e}, 数据: {item}")
            return None
    
    def _sort_and_validate_data(self, data: List[Dict]) -> List[Dict]:
        """排序和验证数据"""
        print("🔄 正在排序和验证数据...")
        
        # 按期号排序（最新的在前）
        data.sort(key=lambda x: x.get('period', ''), reverse=True)
        
        # 去重（基于期号）
        seen_periods = set()
        unique_data = []
        
        for item in data:
            period = item.get('period', '')
            if period and period not in seen_periods:
                seen_periods.add(period)
                unique_data.append(item)
        
        print(f"✅ 数据处理完成，共 {len(unique_data)} 期有效数据")
        
        # 显示数据范围
        if unique_data:
            latest = unique_data[0]
            earliest = unique_data[-1]
            print(f"📊 数据范围：{earliest['period']} ({earliest.get('draw_date', 'N/A')}) 至 {latest['period']} ({latest.get('draw_date', 'N/A')})")
        
        return unique_data
    
    def save_complete_data(self, data: List[Dict]):
        """保存完整数据"""
        if not data:
            print("❌ 没有数据可保存")
            return
        
        print("💾 正在保存完整历史数据...")
        
        # 保存为JSON格式
        json_file = os.path.join(self.data_dir, "ssq_complete_history.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ JSON文件已保存: {json_file}")
        
        # 保存为CSV格式
        try:
            import pandas as pd
            
            # 转换为DataFrame
            processed_data = []
            for item in data:
                red_balls = item.get('red_balls', [])
                if isinstance(red_balls, str):
                    red_balls = red_balls.split(',')
                
                # 补齐红球数据到6个
                while len(red_balls) < 6:
                    red_balls.append('')
                
                row = {
                    '期号': item.get('period', ''),
                    '开奖日期': item.get('draw_date', ''),
                    '红球1': red_balls[0] if len(red_balls) > 0 else '',
                    '红球2': red_balls[1] if len(red_balls) > 1 else '',
                    '红球3': red_balls[2] if len(red_balls) > 2 else '',
                    '红球4': red_balls[3] if len(red_balls) > 3 else '',
                    '红球5': red_balls[4] if len(red_balls) > 4 else '',
                    '红球6': red_balls[5] if len(red_balls) > 5 else '',
                    '蓝球': item.get('blue_ball', ''),
                    '红球号码': item.get('red_balls_str', ''),
                    '数据来源': item.get('data_source', ''),
                    '获取时间': item.get('crawl_time', '')
                }
                processed_data.append(row)
            
            df = pd.DataFrame(processed_data)
            
            # 保存CSV
            csv_file = os.path.join(self.data_dir, "ssq_complete_history.csv")
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV文件已保存: {csv_file}")
            
            # 保存Excel
            excel_file = os.path.join(self.data_dir, "ssq_complete_history.xlsx")
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='双色球完整历史数据', index=False)
                
                # 添加统计信息
                stats_data = [
                    {'统计项': '总期数', '数值': len(data)},
                    {'统计项': '最新期号', '数值': data[0]['period'] if data else ''},
                    {'统计项': '最早期号', '数值': data[-1]['period'] if data else ''},
                    {'统计项': '最新日期', '数值': data[0].get('draw_date', '') if data else ''},
                    {'统计项': '最早日期', '数值': data[-1].get('draw_date', '') if data else ''},
                    {'统计项': '数据来源', '数值': '官方API完整历史'},
                    {'统计项': '生成时间', '数值': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                ]
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            print(f"✅ Excel文件已保存: {excel_file}")
            
        except ImportError:
            print("⚠️ pandas未安装，跳过CSV和Excel文件生成")
        except Exception as e:
            print(f"❌ 保存CSV/Excel文件失败: {e}")
        
        # 更新主数据文件
        main_json_file = os.path.join(self.data_dir, "ssq_history.json")
        with open(main_json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ 主数据文件已更新: {main_json_file}")
    
    def get_data_statistics(self, data: List[Dict]) -> Dict:
        """获取数据统计信息"""
        if not data:
            return {}
        
        # 按年份统计
        year_stats = {}
        for item in data:
            period = item.get('period', '')
            if len(period) >= 4:
                year = period[:4]
                year_stats[year] = year_stats.get(year, 0) + 1
        
        # 计算时间跨度
        years = sorted(year_stats.keys())
        time_span = f"{years[0]}-{years[-1]}" if years else "未知"
        
        return {
            'total_periods': len(data),
            'latest_period': data[0]['period'] if data else '',
            'earliest_period': data[-1]['period'] if data else '',
            'latest_date': data[0].get('draw_date', '') if data else '',
            'earliest_date': data[-1].get('draw_date', '') if data else '',
            'time_span': time_span,
            'years_covered': len(years),
            'year_statistics': year_stats
        }


def main():
    """主函数"""
    print("🎯 双色球完整历史数据获取工具")
    print("=" * 60)
    
    crawler = CompleteHistoryCrawler()
    
    try:
        # 获取完整历史数据
        complete_data = crawler.get_complete_history()
        
        if complete_data:
            # 获取统计信息
            stats = crawler.get_data_statistics(complete_data)
            
            print("\n📊 数据统计信息:")
            print(f"   总期数: {stats['total_periods']}")
            print(f"   时间跨度: {stats['time_span']}")
            print(f"   覆盖年数: {stats['years_covered']} 年")
            print(f"   最新期号: {stats['latest_period']}")
            print(f"   最早期号: {stats['earliest_period']}")
            
            if stats['year_statistics']:
                print("\n📅 各年期数统计:")
                for year, count in sorted(stats['year_statistics'].items()):
                    print(f"   {year}年: {count} 期")
            
            # 保存数据
            crawler.save_complete_data(complete_data)
            
            print("\n🎉 完整历史数据获取完成！")
            print("📁 数据文件已保存到 data/ 目录")
            
        else:
            print("❌ 未能获取到任何数据")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
