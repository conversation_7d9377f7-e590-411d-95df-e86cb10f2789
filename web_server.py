"""
双色球数据网页展示服务器
提供Web界面展示双色球历史开奖数据和预测分析
"""

from flask import Flask, render_template, jsonify, request, send_from_directory
import json
import os
from datetime import datetime
from collections import Counter
from official_api_crawler import OfficialAPISSQCrawler
from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
from enhanced_predictor import EnhancedLotteryPredictor
from trend_analyzer import TrendAnalyzer
from data_processor import DataProcessor
import threading
import time

app = Flask(__name__)

# 全局变量存储数据
lottery_data = []
data_loaded = False
loading_status = {"status": "idle", "progress": 0, "message": ""}

def load_lottery_data():
    """加载双色球数据"""
    global lottery_data, data_loaded, loading_status

    try:
        loading_status = {"status": "loading", "progress": 10, "message": "正在加载历史数据..."}

        processor = DataProcessor()
        data = processor.load_from_json()

        if not data:
            loading_status = {"status": "loading", "progress": 30, "message": "本地数据不存在，开始从API获取..."}

            # 从API获取数据
            crawler = OfficialAPISSQCrawler()
            data = crawler.get_historical_data()  # 获取所有可用数据

            if data:
                loading_status = {"status": "loading", "progress": 80, "message": "正在保存数据..."}
                processor.save_to_json(data)
                processor.save_to_csv(data)
                processor.save_to_excel(data)

        loading_status = {"status": "loading", "progress": 90, "message": "正在处理数据..."}

        # 处理数据格式
        for item in data:
            if isinstance(item.get('red_balls'), str):
                item['red_balls'] = item['red_balls'].split(',')

        lottery_data = data
        data_loaded = True
        loading_status = {"status": "completed", "progress": 100, "message": f"数据加载完成，共 {len(data)} 期"}

        print(f"✅ 数据加载完成，共 {len(data)} 期")

    except Exception as e:
        loading_status = {"status": "error", "progress": 0, "message": f"数据加载失败: {str(e)}"}
        print(f"❌ 数据加载失败: {e}")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/data/status')
def data_status():
    """获取数据加载状态"""
    return jsonify(loading_status)

@app.route('/api/data/summary')
def data_summary():
    """获取数据概要"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    summary = {
        "total_periods": len(lottery_data),
        "latest_period": lottery_data[0]['period'] if lottery_data else None,
        "earliest_period": lottery_data[-1]['period'] if lottery_data else None,
        "latest_date": lottery_data[0]['draw_date'] if lottery_data else None,
        "earliest_date": lottery_data[-1]['draw_date'] if lottery_data else None,
        "data_source": lottery_data[0].get('data_source', 'unknown') if lottery_data else None
    }

    return jsonify(summary)

@app.route('/api/data/recent')
def recent_data():
    """获取最近的开奖数据"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    limit = request.args.get('limit', 50, type=int)
    recent_data = lottery_data[:limit]

    return jsonify(recent_data)

@app.route('/api/data/search')
def search_data():
    """搜索开奖数据"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    period = request.args.get('period', '')
    year = request.args.get('year', '')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    filtered_data = lottery_data

    # 按期号搜索
    if period:
        filtered_data = [item for item in filtered_data if period in item.get('period', '')]

    # 按年份搜索
    if year:
        filtered_data = [item for item in filtered_data if item.get('period', '').startswith(year)]

    # 分页
    start = (page - 1) * per_page
    end = start + per_page
    page_data = filtered_data[start:end]

    return jsonify({
        "data": page_data,
        "total": len(filtered_data),
        "page": page,
        "per_page": per_page,
        "total_pages": (len(filtered_data) + per_page - 1) // per_page
    })

@app.route('/api/analysis/frequency')
def frequency_analysis():
    """频率分析"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    # 统计红球频率
    red_counter = Counter()
    blue_counter = Counter()

    for item in lottery_data:
        red_balls = item.get('red_balls', [])
        if isinstance(red_balls, str):
            red_balls = red_balls.split(',')

        for ball in red_balls:
            if ball and ball.isdigit():
                red_counter[int(ball)] += 1

        blue_ball = item.get('blue_ball', '')
        if blue_ball and str(blue_ball).isdigit():
            blue_counter[int(blue_ball)] += 1

    # 转换为前端需要的格式
    red_freq = [{"number": ball, "count": count} for ball, count in red_counter.most_common()]
    blue_freq = [{"number": ball, "count": count} for ball, count in blue_counter.most_common()]

    return jsonify({
        "red_frequency": red_freq,
        "blue_frequency": blue_freq
    })

@app.route('/api/analysis/trends')
def trends_analysis():
    """趋势分析"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    periods = request.args.get('periods', 30, type=int)
    recent_data = lottery_data[:periods]

    red_counter = Counter()
    blue_counter = Counter()

    for item in recent_data:
        red_balls = item.get('red_balls', [])
        if isinstance(red_balls, str):
            red_balls = red_balls.split(',')

        for ball in red_balls:
            if ball and ball.isdigit():
                red_counter[int(ball)] += 1

        blue_ball = item.get('blue_ball', '')
        if blue_ball and str(blue_ball).isdigit():
            blue_counter[int(blue_ball)] += 1

    return jsonify({
        "periods": periods,
        "red_hot": [{"number": ball, "count": count} for ball, count in red_counter.most_common(10)],
        "red_cold": [{"number": ball, "count": count} for ball, count in red_counter.most_common()[-10:]],
        "blue_hot": [{"number": ball, "count": count} for ball, count in blue_counter.most_common(8)],
        "blue_cold": [{"number": ball, "count": count} for ball, count in blue_counter.most_common()[-8:]]
    })

@app.route('/api/prediction/generate')
def generate_prediction():
    """生成增强版预测"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    try:
        # 使用增强版预测器
        predictor = EnhancedLotteryPredictor()
        predictor.data = lottery_data

        # 提取历史组合和号码
        predictor.historical_combinations = set()
        predictor.historical_blue_balls = set()
        predictor.red_balls_history = []
        predictor.blue_balls_history = []

        for item in lottery_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')

            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            if len(red_balls_int) == 6:
                # 存储历史红球组合
                red_combination = tuple(sorted(red_balls_int))
                predictor.historical_combinations.add(red_combination)
                predictor.red_balls_history.extend(red_balls_int)

            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                blue_ball_int = int(blue_ball)
                predictor.historical_blue_balls.add(blue_ball_int)
                predictor.blue_balls_history.append(blue_ball_int)

        # 生成增强版预测
        enhanced_predictions = predictor.generate_enhanced_predictions(5)

        # 转换为API格式
        next_period = predictor._get_next_period()

        api_predictions = {
            "next_period": next_period,
            "predictions": [],
            "analysis_summary": {
                "total_periods_analyzed": len(lottery_data),
                "excluded_combinations": len(predictor.historical_combinations),
                "excluded_features": ["历史组合", "五连号", "六连号", "和值范围70-130"],
                "prediction_type": "enhanced_exclusion",
                "sum_range": "70-130"
            }
        }

        for pred in enhanced_predictions:
            api_predictions["predictions"].append({
                "red_balls": pred["red_balls"],
                "blue_ball": pred["blue_ball"],
                "red_balls_str": pred["red_balls_str"],
                "blue_ball_str": pred["blue_ball_str"],
                "strategy": pred["strategy"],
                "analysis": pred["analysis"]
            })

        return jsonify(api_predictions)

    except Exception as e:
        return jsonify({"error": f"预测生成失败: {str(e)}"})

@app.route('/api/analysis/trend')
def trend_analysis():
    """趋势分析"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    try:
        periods = request.args.get('periods', 50, type=int)

        # 创建趋势分析器
        analyzer = TrendAnalyzer()
        analyzer.data = lottery_data

        # 生成趋势分析
        trends = analyzer.analyze_number_trends(periods)

        return jsonify(trends)

    except Exception as e:
        return jsonify({"error": f"趋势分析失败: {str(e)}"})

@app.route('/api/analysis/trend/report')
def trend_report():
    """生成趋势分析报告"""
    if not data_loaded or not lottery_data:
        return jsonify({"error": "数据未加载"})

    try:
        periods = request.args.get('periods', 50, type=int)

        # 创建趋势分析器
        analyzer = TrendAnalyzer()
        analyzer.data = lottery_data

        # 生成文本报告
        report = analyzer.generate_trend_report(periods)

        return jsonify({
            "report": report,
            "periods": periods,
            "generate_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        return jsonify({"error": f"报告生成失败: {str(e)}"})

@app.route('/api/data/refresh')
def refresh_data():
    """刷新数据"""
    global data_loaded, loading_status

    if loading_status["status"] == "loading":
        return jsonify({"error": "数据正在加载中，请稍候"})

    data_loaded = False
    loading_status = {"status": "idle", "progress": 0, "message": ""}

    # 在后台线程中重新加载数据
    thread = threading.Thread(target=load_lottery_data)
    thread.daemon = True
    thread.start()

    return jsonify({"message": "开始刷新数据"})

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

def create_templates():
    """创建模板文件"""
    templates_dir = "templates"
    static_dir = "static"

    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)

    if not os.path.exists(static_dir):
        os.makedirs(static_dir)

    # 创建主页模板
    index_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双色球历史数据展示系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .red-ball {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
        }
        .blue-ball {
            background: #0d6efd;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
        }
        .loading-spinner {
            display: none;
        }
        .data-card {
            transition: transform 0.2s;
        }
        .data-card:hover {
            transform: translateY(-2px);
        }
        .lottery-table {
            font-size: 11px;
        }
        .lottery-table th {
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
        }
        .lottery-table td {
            padding: 2px;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }
        .zone1-header {
            background-color: #ffebee !important;
            color: #d32f2f;
        }
        .zone2-header {
            background-color: #e8f5e8 !important;
            color: #388e3c;
        }
        .zone3-header {
            background-color: #fff3e0 !important;
            color: #f57c00;
        }
        .zone1-hit {
            background-color: #ffcdd2 !important;
            color: #d32f2f !important;
            font-weight: bold;
        }
        .zone1-miss {
            background-color: #ffebee !important;
            color: #ccc !important;
        }
        .zone2-hit {
            background-color: #c8e6c9 !important;
            color: #388e3c !important;
            font-weight: bold;
        }
        .zone2-miss {
            background-color: #e8f5e8 !important;
            color: #ccc !important;
        }
        .zone3-hit {
            background-color: #ffe0b2 !important;
            color: #f57c00 !important;
            font-weight: bold;
        }
        .zone3-miss {
            background-color: #fff3e0 !important;
            color: #ccc !important;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i> 双色球历史数据展示系统
            </a>
            <button class="btn btn-outline-light" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 加载状态 -->
        <div id="loading-section" class="text-center mb-4">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">
                    <div class="progress" style="width: 300px; margin: 0 auto;">
                        <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small id="loading-message" class="text-muted">正在加载数据...</small>
                </div>
            </div>
        </div>

        <!-- 数据概要 -->
        <div id="summary-section" class="row mb-4" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> 数据概要</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="total-periods" class="text-primary">-</h4>
                                    <small class="text-muted">总期数</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="latest-period" class="text-success">-</h4>
                                    <small class="text-muted">最新期号</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="earliest-period" class="text-info">-</h4>
                                    <small class="text-muted">最早期号</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6 id="latest-date" class="text-dark">-</h6>
                                    <small class="text-muted">最新开奖日期</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6 id="earliest-date" class="text-dark">-</h6>
                                    <small class="text-muted">最早开奖日期</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能选项卡 -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent" type="button">
                    <i class="bi bi-clock-history"></i> 最近开奖
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button">
                    <i class="bi bi-search"></i> 数据查询
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button">
                    <i class="bi bi-bar-chart"></i> 统计分析
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trend-tab" data-bs-toggle="tab" data-bs-target="#trend" type="button">
                    <i class="bi bi-graph-up-arrow"></i> 趋势分析
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="prediction-tab" data-bs-toggle="tab" data-bs-target="#prediction" type="button">
                    <i class="bi bi-magic"></i> 智能预测
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabsContent">
            <!-- 最近开奖 -->
            <div class="tab-pane fade show active" id="recent" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-clock-history"></i> 最近开奖记录</h5>
                        <select id="recent-limit" class="form-select" style="width: auto;" onchange="loadRecentData()">
                            <option value="20">最近20期</option>
                            <option value="50" selected>最近50期</option>
                            <option value="100">最近100期</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div id="recent-data" class="row">
                            <!-- 数据将通过JavaScript加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据查询 -->
            <div class="tab-pane fade" id="search" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="bi bi-search"></i> 数据查询</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">期号搜索</label>
                                <input type="text" id="search-period" class="form-control" placeholder="输入期号">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">年份筛选</label>
                                <select id="search-year" class="form-select">
                                    <option value="">全部年份</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">每页显示</label>
                                <select id="search-per-page" class="form-select">
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block" onclick="searchData()">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                        <div id="search-results">
                            <!-- 搜索结果将显示在这里 -->
                        </div>
                        <div id="search-pagination" class="d-flex justify-content-center mt-3">
                            <!-- 分页将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计分析 -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart"></i> 红球出现频率 TOP15</h5>
                            </div>
                            <div class="card-body">
                                <div id="red-frequency-chart">
                                    <!-- 红球频率图表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart"></i> 蓝球出现频率 TOP10</h5>
                            </div>
                            <div class="card-body">
                                <div id="blue-frequency-chart">
                                    <!-- 蓝球频率图表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="bi bi-thermometer-half"></i> 号码冷热趋势</h5>
                                <select id="trend-periods" class="form-select" style="width: auto;" onchange="loadTrends()">
                                    <option value="20">最近20期</option>
                                    <option value="30" selected>最近30期</option>
                                    <option value="50">最近50期</option>
                                    <option value="100">最近100期</option>
                                </select>
                            </div>
                            <div class="card-body">
                                <div id="trends-analysis">
                                    <!-- 趋势分析将显示在这里 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势分析 -->
            <div class="tab-pane fade" id="trend" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-graph-up-arrow"></i> 趋势分析</h5>
                        <div class="d-flex gap-2">
                            <select id="trend-analysis-periods" class="form-select" style="width: auto;">
                                <option value="30">最近30期</option>
                                <option value="50" selected>最近50期</option>
                                <option value="100">最近100期</option>
                            </select>
                            <button class="btn btn-primary" onclick="loadTrendAnalysis()">
                                <i class="bi bi-arrow-clockwise"></i> 分析趋势
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="trend-analysis-results">
                            <div class="text-center text-muted">
                                <i class="bi bi-graph-up-arrow" style="font-size: 3rem;"></i>
                                <p>点击"分析趋势"按钮开始趋势分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能预测 -->
            <div class="tab-pane fade" id="prediction" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-magic"></i> 智能预测</h5>
                        <button class="btn btn-success" onclick="generatePrediction()">
                            <i class="bi bi-arrow-clockwise"></i> 生成新预测
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="prediction-results">
                            <div class="text-center text-muted">
                                <i class="bi bi-magic" style="font-size: 3rem;"></i>
                                <p>点击"生成新预测"按钮开始预测分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>"""

    with open(os.path.join(templates_dir, 'index.html'), 'w', encoding='utf-8') as f:
        f.write(index_html)

if __name__ == '__main__':
    print("🚀 启动双色球数据展示系统...")

    # 创建模板文件
    create_templates()

    # 在后台线程中加载数据
    thread = threading.Thread(target=load_lottery_data)
    thread.daemon = True
    thread.start()

    print("📊 数据加载中，请稍候...")
    print("🌐 Web服务器启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")

    app.run(debug=True, host='0.0.0.0', port=5000)
