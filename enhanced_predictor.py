"""
增强版双色球预测器
排除历史数据中已出现的组合，排除五连号、六连号
"""

import json
import random
from collections import Counter, defaultdict
from datetime import datetime
from typing import List, Dict, Tuple, Set
import pandas as pd
from data_processor import DataProcessor


class EnhancedLotteryPredictor:
    """增强版双色球预测器"""

    def __init__(self):
        self.processor = DataProcessor()
        self.data = []
        self.historical_combinations = set()  # 历史红球组合
        self.historical_blue_balls = set()    # 历史蓝球
        self.red_balls_history = []
        self.blue_balls_history = []

    def load_data(self) -> bool:
        """加载历史数据"""
        self.data = self.processor.load_from_json()
        if not self.data:
            print("❌ 没有找到历史数据，请先运行 main.py 获取数据")
            return False

        # 提取历史组合和号码
        for item in self.data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')

            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            if len(red_balls_int) == 6:
                # 存储历史红球组合（排序后的元组）
                red_combination = tuple(sorted(red_balls_int))
                self.historical_combinations.add(red_combination)

                # 存储红球历史
                self.red_balls_history.extend(red_balls_int)

            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                blue_ball_int = int(blue_ball)
                self.historical_blue_balls.add(blue_ball_int)
                self.blue_balls_history.append(blue_ball_int)

        print(f"✅ 成功加载 {len(self.data)} 期历史数据")
        print(f"📊 历史红球组合数: {len(self.historical_combinations)}")
        print(f"📊 历史蓝球种类数: {len(self.historical_blue_balls)}")
        return True

    def has_consecutive_numbers(self, numbers: List[int], max_consecutive: int = 4) -> bool:
        """检查是否有超过指定数量的连续号码"""
        if len(numbers) < max_consecutive + 1:
            return False

        sorted_numbers = sorted(numbers)
        consecutive_count = 1

        for i in range(1, len(sorted_numbers)):
            if sorted_numbers[i] == sorted_numbers[i-1] + 1:
                consecutive_count += 1
                if consecutive_count > max_consecutive:
                    return True
            else:
                consecutive_count = 1

        return False

    def is_valid_combination(self, red_balls: List[int]) -> bool:
        """检查红球组合是否有效"""
        if len(red_balls) != 6:
            return False

        # 检查是否有重复号码
        if len(set(red_balls)) != 6:
            return False

        # 检查号码范围
        if any(ball < 1 or ball > 33 for ball in red_balls):
            return False

        # 检查和值范围 (70-130)
        sum_value = sum(red_balls)
        if sum_value < 70 or sum_value > 130:
            return False

        # 检查是否是历史组合
        red_combination = tuple(sorted(red_balls))
        if red_combination in self.historical_combinations:
            return False

        # 检查是否有五连号或六连号
        if self.has_consecutive_numbers(red_balls, 4):  # 超过4个连续就是5连号或以上
            return False

        return True

    def generate_valid_red_combination(self, strategy: str = "balanced") -> List[int]:
        """生成有效的红球组合"""
        max_attempts = 10000  # 最大尝试次数
        attempts = 0

        while attempts < max_attempts:
            attempts += 1

            if strategy == "frequency_based":
                red_balls = self._generate_frequency_based_combination()
            elif strategy == "trend_based":
                red_balls = self._generate_trend_based_combination()
            elif strategy == "zone_balanced":
                red_balls = self._generate_zone_balanced_combination()
            elif strategy == "random_optimized":
                red_balls = self._generate_random_optimized_combination()
            else:  # balanced
                red_balls = self._generate_balanced_combination()

            if self.is_valid_combination(red_balls):
                return sorted(red_balls)

        # 如果尝试次数用完，生成一个基础的有效组合
        print(f"⚠️ {strategy}策略在{max_attempts}次尝试后未找到有效组合，使用备用方案")
        return self._generate_fallback_combination()

    def _generate_frequency_based_combination(self) -> List[int]:
        """基于频率的组合生成（考虑和值范围）"""
        red_counter = Counter(self.red_balls_history)

        # 获取不同频率段的号码
        all_balls = list(range(1, 34))
        ball_frequencies = [(ball, red_counter.get(ball, 0)) for ball in all_balls]
        ball_frequencies.sort(key=lambda x: x[1], reverse=True)

        # 分为高频、中频、低频
        high_freq = [ball for ball, _ in ball_frequencies[:11]]
        mid_freq = [ball for ball, _ in ball_frequencies[11:22]]
        low_freq = [ball for ball, _ in ball_frequencies[22:]]

        # 目标和值范围 70-130，平均约100
        target_sum = random.randint(85, 115)  # 在合理范围内随机选择目标

        selected = []
        remaining_sum = target_sum

        # 先选择一些基础号码
        for i in range(3):
            if i == 0 and high_freq:
                ball = random.choice(high_freq)
            elif i == 1 and mid_freq:
                ball = random.choice(mid_freq)
            else:
                ball = random.choice(all_balls)

            if ball not in selected:
                selected.append(ball)
                remaining_sum -= ball

        # 补充剩余号码，考虑和值
        remaining_count = 6 - len(selected)
        if remaining_count > 0:
            avg_remaining = remaining_sum // remaining_count

            for _ in range(remaining_count):
                # 在合理范围内选择号码
                min_ball = max(1, avg_remaining - 10)
                max_ball = min(33, avg_remaining + 10)

                candidates = [ball for ball in range(min_ball, max_ball + 1)
                             if ball not in selected]

                if candidates:
                    ball = random.choice(candidates)
                    selected.append(ball)
                    remaining_sum -= ball
                    if len(selected) < 6:
                        avg_remaining = remaining_sum // (6 - len(selected))

        # 如果还不够6个，随机补充
        while len(selected) < 6:
            remaining = [ball for ball in all_balls if ball not in selected]
            if remaining:
                selected.append(random.choice(remaining))
            else:
                break

        return selected[:6]

    def _generate_trend_based_combination(self) -> List[int]:
        """基于趋势的组合生成"""
        # 分析最近30期的趋势
        recent_data = self.data[:30]
        recent_red_balls = []

        for item in recent_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            recent_red_balls.extend([int(ball) for ball in red_balls if ball.isdigit()])

        recent_counter = Counter(recent_red_balls)

        # 获取热门和冷门号码
        hot_balls = [ball for ball, _ in recent_counter.most_common(15)]
        all_balls = set(range(1, 34))
        cold_balls = list(all_balls - set(hot_balls))

        # 选择3个热门、2个冷门、1个随机
        selected = []
        selected.extend(random.sample(hot_balls, min(3, len(hot_balls))))
        selected.extend(random.sample(cold_balls, min(2, len(cold_balls))))

        # 补充到6个
        remaining = [ball for ball in range(1, 34) if ball not in selected]
        selected.extend(random.sample(remaining, min(1, len(remaining))))

        return selected[:6]

    def _generate_zone_balanced_combination(self) -> List[int]:
        """基于区间平衡的组合生成（考虑和值范围）"""
        # 三个区间：1-11, 12-22, 23-33
        zone1 = list(range(1, 12))   # 1-11
        zone2 = list(range(12, 23))  # 12-22
        zone3 = list(range(23, 34))  # 23-33

        # 目标和值
        target_sum = random.randint(85, 115)

        selected = []

        # 每个区间选择2个号码，考虑和值分配
        # 区间1贡献约20-25%，区间2约35-40%，区间3约35-40%
        zone1_target = int(target_sum * 0.22)  # 约22%
        zone2_target = int(target_sum * 0.38)  # 约38%
        zone3_target = target_sum - zone1_target - zone2_target  # 剩余部分

        # 区间1选择2个号码
        zone1_avg = zone1_target // 2
        zone1_candidates = [ball for ball in zone1 if abs(ball - zone1_avg) <= 3]
        if len(zone1_candidates) < 2:
            zone1_candidates = zone1
        selected.extend(random.sample(zone1_candidates, min(2, len(zone1_candidates))))

        # 区间2选择2个号码
        zone2_avg = zone2_target // 2
        zone2_candidates = [ball for ball in zone2 if abs(ball - zone2_avg) <= 3]
        if len(zone2_candidates) < 2:
            zone2_candidates = zone2
        selected.extend(random.sample(zone2_candidates, min(2, len(zone2_candidates))))

        # 区间3选择2个号码
        zone3_avg = zone3_target // 2
        zone3_candidates = [ball for ball in zone3 if abs(ball - zone3_avg) <= 3]
        if len(zone3_candidates) < 2:
            zone3_candidates = zone3
        selected.extend(random.sample(zone3_candidates, min(2, len(zone3_candidates))))

        # 如果不够6个，补充
        while len(selected) < 6:
            all_zones = zone1 + zone2 + zone3
            remaining = [ball for ball in all_zones if ball not in selected]
            if remaining:
                selected.append(random.choice(remaining))
            else:
                break

        return selected[:6]

    def _generate_random_optimized_combination(self) -> List[int]:
        """优化的随机组合生成"""
        # 完全随机，但避免过于集中
        selected = []
        available = list(range(1, 34))

        for _ in range(6):
            if available:
                ball = random.choice(available)
                selected.append(ball)

                # 移除相邻的号码，避免过于集中
                to_remove = [ball]
                if ball > 1 and ball - 1 in available:
                    to_remove.append(ball - 1)
                if ball < 33 and ball + 1 in available:
                    to_remove.append(ball + 1)

                for remove_ball in to_remove:
                    if remove_ball in available:
                        available.remove(remove_ball)

        return selected

    def _generate_balanced_combination(self) -> List[int]:
        """平衡策略组合生成"""
        # 综合考虑频率、趋势、区间分布
        red_counter = Counter(self.red_balls_history)

        # 最近20期趋势
        recent_data = self.data[:20]
        recent_red_balls = []
        for item in recent_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            recent_red_balls.extend([int(ball) for ball in red_balls if ball.isdigit()])

        recent_counter = Counter(recent_red_balls)

        # 计算综合权重
        ball_weights = {}
        for ball in range(1, 34):
            freq_weight = red_counter.get(ball, 0) / len(self.red_balls_history) if self.red_balls_history else 0
            trend_weight = recent_counter.get(ball, 0) / len(recent_red_balls) if recent_red_balls else 0
            ball_weights[ball] = freq_weight * 0.7 + trend_weight * 0.3

        # 加权随机选择
        balls = list(ball_weights.keys())
        weights = list(ball_weights.values())

        selected = []
        available_balls = balls.copy()
        available_weights = weights.copy()

        for _ in range(6):
            if available_balls:
                ball = random.choices(available_balls, weights=available_weights, k=1)[0]
                selected.append(ball)

                # 移除已选择的球
                idx = available_balls.index(ball)
                available_balls.pop(idx)
                available_weights.pop(idx)

        return selected

    def _generate_fallback_combination(self) -> List[int]:
        """备用组合生成方案（确保和值在范围内）"""
        # 目标和值
        target_sum = random.randint(85, 115)

        selected = []

        # 生成基础组合，确保和值合理
        # 选择一些中等号码作为基础
        base_numbers = [8, 12, 17, 21, 26, 30]  # 和值约114

        # 根据目标和值调整
        current_sum = sum(base_numbers)
        diff = target_sum - current_sum

        # 微调号码
        for i in range(len(base_numbers)):
            adjustment = diff // 6
            new_ball = base_numbers[i] + adjustment

            # 确保在有效范围内
            new_ball = max(1, min(33, new_ball))

            # 避免重复
            while new_ball in selected:
                new_ball = random.randint(1, 33)

            selected.append(new_ball)

        # 确保没有重复
        selected = list(set(selected))

        # 如果不够6个，补充
        while len(selected) < 6:
            ball = random.randint(1, 33)
            if ball not in selected:
                selected.append(ball)

        return sorted(selected[:6])

    def generate_enhanced_predictions(self, num_predictions: int = 5) -> List[Dict]:
        """生成增强版预测"""
        if not self.data:
            return []

        print("🎯 开始生成增强版预测（排除历史组合和连号）...")

        strategies = [
            ("频率优化策略", "frequency_based"),
            ("趋势分析策略", "trend_based"),
            ("区间平衡策略", "zone_balanced"),
            ("随机优化策略", "random_optimized"),
            ("综合平衡策略", "balanced")
        ]

        predictions = []

        for i in range(num_predictions):
            strategy_name, strategy_code = strategies[i % len(strategies)]

            # 生成红球组合
            red_balls = self.generate_valid_red_combination(strategy_code)

            # 生成蓝球（避免最近5期出现的蓝球）
            recent_blues = set()
            for item in self.data[:5]:
                blue_ball = item.get('blue_ball', '')
                if blue_ball and str(blue_ball).isdigit():
                    recent_blues.add(int(blue_ball))

            available_blues = [ball for ball in range(1, 17) if ball not in recent_blues]
            if not available_blues:
                available_blues = list(range(1, 17))

            blue_ball = random.choice(available_blues)

            # 计算组合特征
            red_sum = sum(red_balls)
            red_span = max(red_balls) - min(red_balls)
            odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            even_count = 6 - odd_count

            # 区间分布
            zone1_count = sum(1 for ball in red_balls if 1 <= ball <= 11)
            zone2_count = sum(1 for ball in red_balls if 12 <= ball <= 22)
            zone3_count = sum(1 for ball in red_balls if 23 <= ball <= 33)

            predictions.append({
                'strategy': strategy_name,
                'red_balls': red_balls,
                'blue_ball': blue_ball,
                'red_balls_str': ','.join([f"{ball:02d}" for ball in red_balls]),
                'blue_ball_str': f"{blue_ball:02d}",
                'analysis': {
                    'sum': red_sum,
                    'span': red_span,
                    'odd_even': f"{odd_count}奇{even_count}偶",
                    'zone_distribution': f"{zone1_count}-{zone2_count}-{zone3_count}",
                    'is_historical': False,  # 已确保不是历史组合
                    'has_consecutive': self.has_consecutive_numbers(red_balls, 4)
                }
            })

        return predictions

    def validate_predictions(self, predictions: List[Dict]) -> Dict:
        """验证预测结果"""
        validation_result = {
            'total_predictions': len(predictions),
            'valid_predictions': 0,
            'invalid_predictions': 0,
            'issues': []
        }

        for i, pred in enumerate(predictions):
            issues = []

            # 检查红球组合
            red_balls = pred['red_balls']

            # 检查是否是历史组合
            red_combination = tuple(sorted(red_balls))
            if red_combination in self.historical_combinations:
                issues.append("红球组合在历史中出现过")

            # 检查连号
            if self.has_consecutive_numbers(red_balls, 4):
                issues.append("包含五连号或以上")

            # 检查红球号码范围
            if any(ball < 1 or ball > 33 for ball in red_balls):
                issues.append("红球号码超出范围")

            # 检查和值范围
            red_sum = sum(red_balls)
            if red_sum < 70 or red_sum > 130:
                issues.append(f"和值{red_sum}超出范围(70-130)")

            # 检查蓝球范围
            blue_ball = pred['blue_ball']
            if blue_ball < 1 or blue_ball > 16:
                issues.append("蓝球号码超出范围")

            if issues:
                validation_result['invalid_predictions'] += 1
                validation_result['issues'].append({
                    'prediction_index': i + 1,
                    'strategy': pred['strategy'],
                    'issues': issues
                })
            else:
                validation_result['valid_predictions'] += 1

        return validation_result

    def print_enhanced_predictions(self):
        """打印增强版预测结果"""
        if not self.load_data():
            return

        print("=" * 80)
        print("🎯 双色球增强版预测分析")
        print("=" * 80)
        print(f"📊 基于 {len(self.data)} 期历史数据")
        print(f"🚫 排除 {len(self.historical_combinations)} 个历史红球组合")
        print(f"🚫 排除五连号、六连号组合")
        print(f"🎯 和值范围限制: 70-130")
        print(f"📅 预测期号: {self._get_next_period()}")

        # 生成预测
        predictions = self.generate_enhanced_predictions(5)

        if predictions:
            print(f"\n🎯 预测方案:")
            print("=" * 60)

            for i, pred in enumerate(predictions, 1):
                red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
                blue_str = pred['blue_ball_str']
                analysis = pred['analysis']

                print(f"方案{i} - {pred['strategy']}:")
                print(f"   🔴 {red_str} | 🔵 {blue_str}")
                print(f"   特征: 和值{analysis['sum']}, 跨度{analysis['span']}, {analysis['odd_even']}, 区间{analysis['zone_distribution']}")
                print()

            # 验证预测
            validation = self.validate_predictions(predictions)
            print(f"✅ 预测验证结果:")
            print(f"   有效预测: {validation['valid_predictions']}/{validation['total_predictions']}")

            if validation['issues']:
                print(f"   发现问题: {len(validation['issues'])} 个")
                for issue in validation['issues']:
                    print(f"     方案{issue['prediction_index']}: {', '.join(issue['issues'])}")
            else:
                print(f"   🎉 所有预测均通过验证！")

        print(f"\n⚠️ 重要提醒:")
        print(f"   • 已排除历史上出现过的 {len(self.historical_combinations)} 个红球组合")
        print(f"   • 已排除包含五连号、六连号的组合")
        print(f"   • 预测结果仅供参考，请理性购彩")
        print("=" * 80)

    def _get_next_period(self) -> str:
        """获取下一期期号"""
        if not self.data:
            return "未知"

        latest_period = self.data[0]['period']
        year = int(latest_period[:4])
        period_num = int(latest_period[4:])

        next_period_num = period_num + 1
        if next_period_num > 156:
            year += 1
            next_period_num = 1

        return f"{year}{next_period_num:03d}"


def main():
    """主函数"""
    predictor = EnhancedLotteryPredictor()
    predictor.print_enhanced_predictions()


if __name__ == "__main__":
    main()
