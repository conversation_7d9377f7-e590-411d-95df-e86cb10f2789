"""
双色球分析报告查看器
提供简单的命令行界面查看生成的报告
"""

import os
import json
import glob
from datetime import datetime


class ReportViewer:
    """报告查看器"""
    
    def __init__(self):
        self.reports_dir = "reports"
        
    def list_reports(self):
        """列出所有可用的报告"""
        if not os.path.exists(self.reports_dir):
            print("❌ 报告目录不存在，请先运行 document_generator.py 生成报告")
            return []
        
        # 查找所有报告文件
        report_files = []
        patterns = [
            "双色球分析报告_*.md",
            "双色球分析报告_*.html", 
            "双色球分析报告_*.txt",
            "双色球分析报告_*.xlsx",
            "双色球分析数据_*.json"
        ]
        
        for pattern in patterns:
            files = glob.glob(os.path.join(self.reports_dir, pattern))
            report_files.extend(files)
        
        if not report_files:
            print("❌ 没有找到报告文件，请先运行 document_generator.py 生成报告")
            return []
        
        # 按时间戳分组
        report_groups = {}
        for file_path in report_files:
            filename = os.path.basename(file_path)
            # 提取时间戳
            if "_" in filename:
                parts = filename.split("_")
                if len(parts) >= 2:
                    timestamp = parts[-1].split(".")[0]  # 去掉扩展名
                    if timestamp not in report_groups:
                        report_groups[timestamp] = []
                    report_groups[timestamp].append(file_path)
        
        return report_groups
    
    def show_report_menu(self):
        """显示报告菜单"""
        print("=" * 60)
        print("📊 双色球分析报告查看器")
        print("=" * 60)
        
        report_groups = self.list_reports()
        if not report_groups:
            return
        
        # 按时间戳排序（最新的在前）
        sorted_timestamps = sorted(report_groups.keys(), reverse=True)
        
        print("📁 可用的报告:")
        for i, timestamp in enumerate(sorted_timestamps, 1):
            # 格式化时间戳显示
            try:
                dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
                formatted_time = dt.strftime("%Y年%m月%d日 %H:%M:%S")
            except:
                formatted_time = timestamp
            
            print(f"   {i}. {formatted_time}")
            
            # 显示该时间戳下的文件类型
            file_types = []
            for file_path in report_groups[timestamp]:
                ext = os.path.splitext(file_path)[1].lower()
                if ext == '.md':
                    file_types.append('Markdown')
                elif ext == '.html':
                    file_types.append('HTML')
                elif ext == '.txt':
                    file_types.append('文本')
                elif ext == '.xlsx':
                    file_types.append('Excel')
                elif ext == '.json':
                    file_types.append('JSON')
            
            print(f"      格式: {', '.join(file_types)}")
        
        print("\n📋 操作选项:")
        print("   s - 显示最新报告摘要")
        print("   o - 打开报告文件夹")
        print("   q - 退出")
        
        while True:
            choice = input("\n请选择操作 (s/o/q): ").strip().lower()
            
            if choice == 'q':
                print("👋 再见！")
                break
            elif choice == 's':
                self.show_latest_summary(sorted_timestamps[0], report_groups[sorted_timestamps[0]])
            elif choice == 'o':
                self.open_reports_folder()
            else:
                print("❌ 无效选择，请重新输入")
    
    def show_latest_summary(self, timestamp, files):
        """显示最新报告的摘要"""
        print("\n" + "=" * 60)
        print("📊 最新报告摘要")
        print("=" * 60)
        
        # 查找JSON文件来获取详细数据
        json_file = None
        for file_path in files:
            if file_path.endswith('.json'):
                json_file = file_path
                break
        
        if json_file and os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 显示基本信息
                report_info = data.get('report_info', {})
                print(f"📅 生成时间: {report_info.get('generate_time', 'N/A')}")
                print(f"📊 数据来源: {report_info.get('data_source', 'N/A')}")
                print(f"📈 分析期数: {report_info.get('total_periods', 'N/A')} 期")
                print(f"🎯 预测期号: {report_info.get('next_period', 'N/A')}")
                
                # 显示预测方案
                predictions = data.get('predictions', {})
                basic_preds = predictions.get('basic_predictions', [])
                advanced_preds = predictions.get('advanced_predictions', [])
                
                print(f"\n🎯 预测方案:")
                
                if basic_preds:
                    print("   基础统计预测:")
                    for pred in basic_preds[:3]:
                        red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
                        blue_str = f"{pred['blue_ball']:02d}"
                        print(f"     {pred['method']}: 🔴 {red_str} | 🔵 {blue_str}")
                
                if advanced_preds:
                    print("   智能策略预测:")
                    for pred in advanced_preds[:3]:
                        red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
                        blue_str = f"{pred['blue_ball']:02d}"
                        print(f"     {pred['strategy']}: 🔴 {red_str} | 🔵 {blue_str}")
                
                # 显示频率统计
                freq_analysis = data.get('frequency_analysis', {})
                red_top = freq_analysis.get('red_top10', [])
                blue_top = freq_analysis.get('blue_top8', [])
                
                if red_top:
                    print(f"\n🔴 红球高频 TOP5:")
                    for i, item in enumerate(red_top[:5], 1):
                        print(f"     {i}. 号码{item['ball']:02d}: {item['count']}次")
                
                if blue_top:
                    print(f"\n🔵 蓝球高频 TOP3:")
                    for i, item in enumerate(blue_top[:3], 1):
                        print(f"     {i}. 号码{item['ball']:02d}: {item['count']}次")
                
                # 显示遗漏分析
                missing_analysis = data.get('missing_analysis', {})
                red_missing = missing_analysis.get('red_most_missing', [])
                blue_missing = missing_analysis.get('blue_most_missing', [])
                
                if red_missing:
                    print(f"\n❄️ 红球遗漏 TOP5:")
                    for i, item in enumerate(red_missing[:5], 1):
                        print(f"     {i}. 号码{item['ball']:02d}: {item['periods']}期")
                
                if blue_missing:
                    print(f"\n❄️ 蓝球遗漏 TOP3:")
                    for i, item in enumerate(blue_missing[:3], 1):
                        print(f"     {i}. 号码{item['ball']:02d}: {item['periods']}期")
                
            except Exception as e:
                print(f"❌ 读取JSON报告失败: {e}")
        else:
            print("❌ 未找到JSON格式的详细数据")
        
        print("\n📁 报告文件:")
        for file_path in files:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            print(f"   📄 {filename} ({file_size:,} 字节)")
        
        print("\n💡 提示: 输入 'o' 可以打开报告文件夹查看详细内容")
    
    def open_reports_folder(self):
        """打开报告文件夹"""
        if os.path.exists(self.reports_dir):
            try:
                import subprocess
                import platform
                
                if platform.system() == "Windows":
                    os.startfile(self.reports_dir)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.reports_dir])
                else:  # Linux
                    subprocess.run(["xdg-open", self.reports_dir])
                
                print(f"✅ 已打开报告文件夹: {os.path.abspath(self.reports_dir)}")
            except Exception as e:
                print(f"❌ 无法打开文件夹: {e}")
                print(f"📁 报告文件夹路径: {os.path.abspath(self.reports_dir)}")
        else:
            print("❌ 报告文件夹不存在")


def main():
    """主函数"""
    viewer = ReportViewer()
    viewer.show_report_menu()


if __name__ == "__main__":
    main()
