# 双色球智能预测分析系统

## 🎯 项目概述

这是一个基于中国福利彩票官方API的双色球历史数据获取和智能预测分析系统。系统通过统计学方法分析历史开奖数据，提供多维度的号码预测建议。

## ✨ 主要功能

### 1. 数据获取
- 🌐 **官方API数据源**: 使用中国福利彩票官方API获取真实历史数据
- 📊 **大量历史数据**: 支持获取最多3000期历史开奖数据
- 🔄 **自动分页处理**: 智能处理API分页，确保数据完整性
- 💾 **多格式存储**: 支持JSON、CSV、Excel格式数据存储

### 2. 数据分析
- 📈 **频率统计分析**: 分析每个号码的历史出现频率
- 🔥 **趋势分析**: 分析最近期数的热门和冷门号码
- ❄️ **遗漏分析**: 统计各号码当前遗漏期数
- 📊 **组合特征分析**: 分析和值、跨度、AC值等组合特征
- 🎯 **模式识别**: 识别号码分布模式和规律

### 3. 智能预测
- 🧠 **多策略预测**: 提供5种不同的预测策略
- ⚖️ **权重算法**: 基于频率和趋势的加权预测
- 🎲 **随机优化**: 避免过度拟合的随机化处理
- 📋 **多方案输出**: 每次生成多组预测方案供选择

### 4. 报告生成
- 📄 **多格式报告**: 生成Markdown、HTML、Excel、JSON、TXT格式报告
- 🎨 **美观界面**: HTML报告包含精美的样式和图表
- 📊 **详细统计**: 包含完整的统计数据和分析结果
- 🔍 **报告查看器**: 提供便捷的报告浏览工具

## 📁 文件结构

```
双色球预测系统/
├── main.py                     # 主程序 - 数据获取
├── official_api_crawler.py     # 官方API爬虫
├── lottery_predictor.py        # 基础预测分析器
├── advanced_predictor.py       # 高级预测分析器
├── final_prediction_report.py  # 综合预测报告
├── document_generator.py       # 文档生成器
├── report_viewer.py            # 报告查看器
├── data_processor.py           # 数据处理器
├── test_data.py               # 数据测试工具
├── config.py                  # 配置文件
├── requirements.txt           # 依赖包列表
├── README.md                  # 使用说明
├── data/                      # 数据目录
│   ├── ssq_history.json      # JSON格式历史数据
│   ├── ssq_history.csv       # CSV格式历史数据
│   └── ssq_history.xlsx      # Excel格式历史数据
└── reports/                   # 报告目录
    ├── *.md                   # Markdown报告
    ├── *.html                 # HTML报告
    ├── *.xlsx                 # Excel报告
    ├── *.json                 # JSON数据报告
    └── *.txt                  # 文本报告
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 获取历史数据
```bash
# 获取所有可用数据（约1884期）
python main.py

# 获取指定期数
python main.py --periods 1000

# 获取数据并验证
python main.py --periods 500 --validate
```

### 3. 生成预测分析
```bash
# 基础预测分析
python lottery_predictor.py

# 高级预测分析
python advanced_predictor.py

# 综合预测报告
python final_prediction_report.py
```

### 4. 生成文档报告
```bash
# 生成所有格式的报告
python document_generator.py
```

### 5. 查看报告
```bash
# 启动报告查看器
python report_viewer.py
```

## 📊 预测策略说明

### 基础预测策略
1. **频率加权预测**: 基于历史出现频率的加权随机选择
2. **趋势结合预测**: 结合最近期数的热门趋势
3. **平衡选择预测**: 平衡热门和冷门号码的组合

### 高级预测策略
1. **遗漏热门策略**: 结合遗漏号码和热门号码
2. **和值控制策略**: 基于历史和值范围的约束选择
3. **区间均衡策略**: 保持号码在各区间的均衡分布
4. **AC值优化策略**: 基于算术复杂性的优化选择
5. **综合策略**: 综合考虑多个因素的智能选择

## 📈 数据统计示例

基于1884期历史数据（2013-2025年）的统计结果：

### 红球频率TOP5
1. 号码14: 389次 (3.44%)
2. 号码06: 387次 (3.42%)
3. 号码22: 385次 (3.41%)
4. 号码09: 367次 (3.25%)
5. 号码01: 367次 (3.25%)

### 蓝球频率TOP5
1. 号码01: 136次 (7.22%)
2. 号码15: 132次 (7.01%)
3. 号码16: 132次 (7.01%)
4. 号码07: 128次 (6.79%)
5. 号码12: 125次 (6.63%)

## 🎯 预测示例

### 下一期预测（2025078期）

**基础统计预测**:
- 方案1: 🔴 05 10 14 18 29 33 | 🔵 16
- 方案2: 🔴 02 08 10 11 20 33 | 🔵 01
- 方案3: 🔴 04 06 14 19 20 25 | 🔵 01

**智能策略预测**:
- 策略1: 🔴 02 05 06 10 12 29 | 🔵 04 (遗漏+热门)
- 策略2: 🔴 01 04 08 24 26 31 | 🔵 12 (和值控制)
- 策略3: 🔴 06 11 16 20 25 30 | 🔵 07 (区间均衡)

## 💡 使用建议

### 号码选择建议
- **热门红球**: 10、2、6、22、20 (最近30期出现频繁)
- **遗漏红球**: 7、5、19、12、29 (当前遗漏期数较多)
- **建议组合**: 2-3个热门号码 + 2-3个遗漏号码

### 技术指标建议
- **和值范围**: 90-110 (历史平均101)
- **跨度范围**: 19-29 (历史平均24)
- **区间分布**: 建议采用2-2-2或2-3-1分布
- **奇偶比例**: 建议3:3或4:2

## ⚠️ 重要声明

- 🔸 本系统基于历史数据统计分析，采用多种数学模型
- 🔸 彩票开奖具有随机性，任何预测都不能保证准确性
- 🔸 预测结果仅供参考，不构成投注建议
- 🔸 请理性购彩，量力而行，切勿沉迷
- 🔸 购彩有风险，投注需谨慎

## 🛠️ 技术特点

- **数据来源**: 中国福利彩票官方API
- **编程语言**: Python 3.7+
- **主要库**: requests, pandas, openpyxl, beautifulsoup4
- **数据格式**: JSON, CSV, Excel, HTML, Markdown
- **分析方法**: 频率统计、趋势分析、遗漏分析、组合分析
- **预测算法**: 加权随机、约束优化、模式识别

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：
- 查看README.md了解详细使用说明
- 运行test_data.py验证数据完整性
- 使用report_viewer.py查看生成的报告

---

*最后更新: 2025年7月10日*
*版本: 1.0.0*
*作者: 双色球智能预测系统*
