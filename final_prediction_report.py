"""
双色球最终预测报告生成器
整合所有分析结果，生成综合预测报告
"""

import json
from collections import Counter
from datetime import datetime
from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
from data_processor import DataProcessor


class FinalPredictionReport:
    """最终预测报告生成器"""
    
    def __init__(self):
        self.basic_predictor = LotteryPredictor()
        self.advanced_predictor = AdvancedLotteryPredictor()
        self.processor = DataProcessor()
        
    def generate_comprehensive_report(self):
        """生成综合预测报告"""
        print("🎯" * 30)
        print("双色球综合预测分析报告")
        print("🎯" * 30)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 数据来源: 中国福利彩票官方API")
        
        # 加载数据
        if not self.basic_predictor.load_data():
            return
        
        data = self.basic_predictor.data
        print(f"📈 分析期数: {len(data)} 期")
        print(f"⏰ 数据范围: {data[-1]['draw_date']} 至 {data[0]['draw_date']}")
        print(f"🎲 最新期号: {data[0]['period']}")
        
        print("\n" + "="*80)
        print("📊 核心统计数据")
        print("="*80)
        
        # 基础频率分析
        freq_analysis = self.basic_predictor.analyze_frequency()
        
        print("🔴 红球出现频率 TOP10:")
        for i, (ball, count) in enumerate(freq_analysis['red_most_common'][:10], 1):
            percentage = freq_analysis['red_frequency'][ball]['percentage']
            print(f"   {i:2d}. 号码{ball:2d}: {count:3d}次 ({percentage:.1f}%)")
        
        print("\n🔵 蓝球出现频率 TOP8:")
        for i, (ball, count) in enumerate(freq_analysis['blue_most_common'][:8], 1):
            percentage = freq_analysis['blue_frequency'][ball]['percentage']
            print(f"   {i:2d}. 号码{ball:2d}: {count:3d}次 ({percentage:.1f}%)")
        
        # 最近趋势分析
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        print(f"\n🔥 最近30期热门号码:")
        print(f"   红球: {[f'{ball}({count})' for ball, count in trend_analysis['red_hot'][:10]]}")
        print(f"   蓝球: {[f'{ball}({count})' for ball, count in trend_analysis['blue_hot'][:5]]}")
        
        print(f"\n❄️ 最近30期冷门号码:")
        print(f"   红球: {[f'{ball}({count})' for ball, count in trend_analysis['red_cold'][:10]]}")
        print(f"   蓝球: {[f'{ball}({count})' for ball, count in trend_analysis['blue_cold'][:5]]}")
        
        # 高级分析
        self.advanced_predictor.load_data()
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        
        print("\n" + "="*80)
        print("🔍 高级统计分析")
        print("="*80)
        
        print("📉 当前遗漏情况:")
        print(f"   红球遗漏最久: {[f'{ball}({count}期)' for ball, count in missing_analysis['red_most_missing'][:8]]}")
        print(f"   蓝球遗漏最久: {[f'{ball}({count}期)' for ball, count in missing_analysis['blue_most_missing'][:5]]}")
        
        avg_sum = sum(combo_analysis['sum_analysis']) / len(combo_analysis['sum_analysis'])
        avg_span = sum(combo_analysis['span_analysis']) / len(combo_analysis['span_analysis'])
        avg_ac = sum(combo_analysis['ac_values']) / len(combo_analysis['ac_values'])
        
        print(f"\n📊 号码组合特征:")
        print(f"   历史平均和值: {avg_sum:.1f}")
        print(f"   历史平均跨度: {avg_span:.1f}")
        print(f"   历史平均AC值: {avg_ac:.1f}")
        
        most_common_zone = combo_analysis['zone_patterns'].most_common(1)[0]
        print(f"   最常见区间分布: {most_common_zone[0]} (出现{most_common_zone[1]}次)")
        
        # 生成预测
        print("\n" + "="*80)
        print("🎯 综合预测结果")
        print("="*80)
        
        next_period = self._get_next_period(data)
        print(f"🎲 预测期号: {next_period}")
        
        # 基础预测
        basic_predictions = self.basic_predictor.predict_numbers()
        print(f"\n📈 基础统计预测 (基于频率和趋势):")
        for i, pred in enumerate(basic_predictions['predictions'][:3], 1):
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = pred['blue_ball_str']
            print(f"   方案{i}: 🔴 {red_str} | 🔵 {blue_str}")
        
        # 高级预测
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(3)
        print(f"\n🧠 智能策略预测 (基于多维度分析):")
        for i, pred in enumerate(advanced_predictions, 1):
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = pred['blue_ball_str']
            print(f"   {pred['strategy']}: 🔴 {red_str} | 🔵 {blue_str}")
            print(f"   策略说明: {pred['analysis']}")
        
        # 推荐号码
        print("\n" + "="*80)
        print("⭐ 重点推荐号码")
        print("="*80)
        
        # 综合推荐红球
        all_red_predictions = []
        for pred in basic_predictions['predictions']:
            all_red_predictions.extend(pred['red_balls'])
        for pred in advanced_predictions:
            all_red_predictions.extend(pred['red_balls'])
        
        red_recommendation = Counter(all_red_predictions).most_common(12)
        print("🔴 推荐红球 (按出现频次排序):")
        print(f"   重点关注: {[f'{ball}({count})' for ball, count in red_recommendation[:6]]}")
        print(f"   次要关注: {[f'{ball}({count})' for ball, count in red_recommendation[6:12]]}")
        
        # 综合推荐蓝球
        all_blue_predictions = []
        for pred in basic_predictions['predictions']:
            all_blue_predictions.append(pred['blue_ball'])
        for pred in advanced_predictions:
            all_blue_predictions.append(pred['blue_ball'])
        
        blue_recommendation = Counter(all_blue_predictions).most_common(8)
        print(f"\n🔵 推荐蓝球 (按出现频次排序):")
        print(f"   重点关注: {[f'{ball}({count})' for ball, count in blue_recommendation[:4]]}")
        print(f"   次要关注: {[f'{ball}({count})' for ball, count in blue_recommendation[4:8]]}")
        
        # 投注建议
        print("\n" + "="*80)
        print("💡 投注建议")
        print("="*80)
        
        print("🎯 号码选择建议:")
        hot_reds = [ball for ball, _ in trend_analysis['red_hot'][:5]]
        missing_reds = [ball for ball, _ in missing_analysis['red_most_missing'][:5]]
        print(f"   • 热门红球: {hot_reds} (最近30期出现频繁)")
        print(f"   • 遗漏红球: {missing_reds} (当前遗漏期数较多)")
        print(f"   • 建议组合: 2-3个热门号码 + 2-3个遗漏号码")
        
        hot_blues = [ball for ball, _ in trend_analysis['blue_hot'][:3]]
        missing_blues = [ball for ball, _ in missing_analysis['blue_most_missing'][:3]]
        print(f"   • 热门蓝球: {hot_blues}")
        print(f"   • 遗漏蓝球: {missing_blues}")
        
        print(f"\n📊 技术指标建议:")
        print(f"   • 和值范围: {int(avg_sum-10)} - {int(avg_sum+10)} (历史平均{avg_sum:.0f})")
        print(f"   • 跨度范围: {int(avg_span-5)} - {int(avg_span+5)} (历史平均{avg_span:.0f})")
        print(f"   • 区间分布: 建议采用2-2-2或2-3-1分布")
        print(f"   • 奇偶比例: 建议3:3或4:2")
        
        print("\n" + "="*80)
        print("⚠️  重要声明")
        print("="*80)
        print("🔸 本预测基于历史数据统计分析，采用多种数学模型")
        print("🔸 彩票开奖具有随机性，任何预测都不能保证准确性")
        print("🔸 预测结果仅供参考，不构成投注建议")
        print("🔸 请理性购彩，量力而行，切勿沉迷")
        print("🔸 购彩有风险，投注需谨慎")
        
        print("\n🎯" * 30)
        print("报告生成完成")
        print("🎯" * 30)
    
    def _get_next_period(self, data):
        """获取下一期期号"""
        if not data:
            return "未知"
        
        latest_period = data[0]['period']
        year = int(latest_period[:4])
        period_num = int(latest_period[4:])
        
        next_period_num = period_num + 1
        if next_period_num > 156:
            year += 1
            next_period_num = 1
        
        return f"{year}{next_period_num:03d}"


def main():
    """主函数"""
    report_generator = FinalPredictionReport()
    report_generator.generate_comprehensive_report()


if __name__ == "__main__":
    main()
