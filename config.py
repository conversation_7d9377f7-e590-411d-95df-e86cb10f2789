"""
双色球数据爬取配置文件
"""

# 基础配置
BASE_URL = "https://www.zhcw.com"
SSQ_URL = "https://www.zhcw.com/kjxx/ssq/"

# 官方API配置
OFFICIAL_API_BASE_URL = "https://www.cwl.gov.cn"
OFFICIAL_API_URL = "https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"

# 请求配置
REQUEST_TIMEOUT = 30
REQUEST_DELAY = 1  # 请求间隔时间（秒）
MAX_RETRIES = 3

# 数据配置
MAX_PERIODS = 3000  # 最大获取期数
BATCH_SIZE = 100    # 每批次获取的期数
API_PAGE_SIZE = 30  # API每页返回的数据量

# 文件配置
DATA_DIR = "data"
CSV_FILE = "ssq_history.csv"
JSON_FILE = "ssq_history.json"
EXCEL_FILE = "ssq_history.xlsx"

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://www.cwl.gov.cn/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
}
