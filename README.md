# 双色球历史开奖数据获取工具 (官方API版本)

这是一个基于中国福利彩票官方API的双色球历史开奖数据获取工具。

## 功能特点

- 🎯 使用官方API获取双色球历史开奖数据（最多3000期）
- 📊 支持多种数据格式输出（JSON、CSV、Excel）
- ✅ 内置数据验证功能
- 🔄 支持分页获取，自动处理大量数据
- 📈 自动生成统计信息
- 🛡️ 包含请求限制和错误处理机制

## API信息

- **API地址**: https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice
- **数据来源**: 中国福利彩票官方网站
- **支持参数**: 
  - name: ssq (双色球)
  - pageNo: 页码
  - pageSize: 每页数据量 (最大30)
  - systemType: PC

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

获取最近3000期双色球开奖数据：

```bash
python main.py
```

### 高级选项

```bash
# 获取指定期数
python main.py --periods 1000

# 指定输出格式
python main.py --format csv
python main.py --format json
python main.py --format excel

# 启用数据验证
python main.py --validate

# 组合使用
python main.py --periods 500 --format excel --validate
```

### 参数说明

- `--periods`: 获取的期数（默认3000期）
- `--format`: 输出格式，可选值：json、csv、excel、all（默认all）
- `--validate`: 启用数据验证功能

## 输出文件

程序会在 `data` 目录下生成以下文件：

- `ssq_history.json`: JSON格式的历史数据
- `ssq_history.csv`: CSV格式的历史数据
- `ssq_history.xlsx`: Excel格式的历史数据（包含统计信息）

## 数据格式

每条开奖记录包含以下字段：

```json
{
  "period": "2024001",           // 期号
  "draw_date": "2024-01-01",     // 开奖日期
  "red_balls": ["01", "02", "03", "04", "05", "06"],  // 红球号码
  "blue_ball": "07",             // 蓝球号码
  "red_balls_str": "01,02,03,04,05,06",  // 红球号码字符串
  "crawl_time": "2024-01-01 12:00:00",   // 获取时间
  "data_source": "official_api"  // 数据来源
}
```

## 注意事项

1. 请合理使用API，避免频繁请求
2. 程序内置了请求延迟机制，确保不会对服务器造成压力
3. 如果遇到网络问题，程序会自动重试
4. 数据验证功能可以帮助检查获取数据的完整性

## 错误处理

- 网络连接错误：程序会显示错误信息并退出
- API响应错误：程序会记录错误并尝试继续获取其他数据
- 数据解析错误：程序会跳过有问题的数据并继续处理

## 技术实现

- 使用 `requests` 库进行HTTP请求
- 使用 `pandas` 进行数据处理和导出
- 使用 `openpyxl` 生成Excel文件
- 支持中文编码，确保数据正确显示

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
