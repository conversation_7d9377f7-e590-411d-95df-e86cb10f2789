"""
早期双色球数据获取器
尝试从多个数据源获取2003-2012年的双色球历史数据
"""

import requests
import json
import time
import os
import re
from datetime import datetime, date
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
import config


class EarlyDataCrawler:
    """早期数据爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 确保数据目录存在
        self.data_dir = config.DATA_DIR
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def get_early_data_from_multiple_sources(self) -> List[Dict]:
        """从多个数据源获取早期数据"""
        print("🔍 尝试从多个数据源获取2003-2012年双色球数据...")
        
        all_early_data = []
        
        # 数据源1: 尝试中彩网历史数据
        print("📡 尝试数据源1: 中彩网...")
        zhcw_data = self._get_data_from_zhcw()
        if zhcw_data:
            all_early_data.extend(zhcw_data)
            print(f"✅ 从中彩网获取到 {len(zhcw_data)} 期数据")
        
        # 数据源2: 尝试500彩票网
        print("📡 尝试数据源2: 500彩票网...")
        w500_data = self._get_data_from_500wan()
        if w500_data:
            all_early_data.extend(w500_data)
            print(f"✅ 从500彩票网获取到 {len(w500_data)} 期数据")
        
        # 数据源3: 尝试彩票大赢家
        print("📡 尝试数据源3: 彩票大赢家...")
        dyj_data = self._get_data_from_dayjia()
        if dyj_data:
            all_early_data.extend(dyj_data)
            print(f"✅ 从彩票大赢家获取到 {len(dyj_data)} 期数据")
        
        # 去重和排序
        if all_early_data:
            unique_data = self._deduplicate_data(all_early_data)
            print(f"📊 去重后共获得 {len(unique_data)} 期早期数据")
            return unique_data
        else:
            print("❌ 未能从任何数据源获取到早期数据")
            return []
    
    def _get_data_from_zhcw(self) -> List[Dict]:
        """从中彩网获取数据"""
        try:
            # 中彩网的历史数据页面
            base_url = "https://www.zhcw.com/kjxx/ssq/kjgg/"
            
            early_data = []
            
            # 尝试获取不同年份的数据
            for year in range(2003, 2013):
                print(f"  📅 尝试获取 {year} 年数据...")
                
                # 构建年份URL
                year_url = f"{base_url}{year}/"
                
                try:
                    response = self.session.get(year_url, timeout=10)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        year_data = self._parse_zhcw_page(soup, year)
                        if year_data:
                            early_data.extend(year_data)
                            print(f"    ✅ 获取到 {len(year_data)} 期")
                        else:
                            print(f"    ❌ 未找到数据")
                    else:
                        print(f"    ❌ 请求失败: {response.status_code}")
                
                except Exception as e:
                    print(f"    ❌ 获取失败: {e}")
                
                time.sleep(1)  # 避免请求过快
            
            return early_data
            
        except Exception as e:
            print(f"❌ 中彩网数据获取失败: {e}")
            return []
    
    def _parse_zhcw_page(self, soup: BeautifulSoup, year: int) -> List[Dict]:
        """解析中彩网页面"""
        data = []
        
        try:
            # 查找开奖数据表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows[1:]:  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    
                    if len(cells) >= 8:  # 确保有足够的列
                        try:
                            period = cells[0].get_text().strip()
                            draw_date = cells[1].get_text().strip()
                            
                            # 提取红球号码
                            red_balls = []
                            for i in range(2, 8):  # 红球在第2-7列
                                ball = cells[i].get_text().strip()
                                if ball.isdigit():
                                    red_balls.append(f"{int(ball):02d}")
                            
                            # 提取蓝球号码
                            blue_ball = cells[8].get_text().strip() if len(cells) > 8 else ""
                            if blue_ball.isdigit():
                                blue_ball = f"{int(blue_ball):02d}"
                            
                            # 验证数据完整性
                            if period and len(red_balls) == 6 and blue_ball:
                                data.append({
                                    'period': period,
                                    'draw_date': draw_date,
                                    'red_balls': red_balls,
                                    'blue_ball': blue_ball,
                                    'red_balls_str': ','.join(red_balls),
                                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    'data_source': 'zhcw_early_data'
                                })
                        
                        except Exception as e:
                            continue  # 跳过解析失败的行
            
        except Exception as e:
            print(f"❌ 解析中彩网页面失败: {e}")
        
        return data
    
    def _get_data_from_500wan(self) -> List[Dict]:
        """从500彩票网获取数据"""
        try:
            print("  📡 尝试500彩票网API...")
            
            # 500彩票网的API接口
            api_url = "https://www.500.com/static/public/ssq/xml/ssqdata.xml"
            
            response = self.session.get(api_url, timeout=15)
            if response.status_code == 200:
                # 解析XML数据
                data = self._parse_500wan_xml(response.text)
                return data
            else:
                print(f"    ❌ 500彩票网请求失败: {response.status_code}")
                return []
        
        except Exception as e:
            print(f"❌ 500彩票网数据获取失败: {e}")
            return []
    
    def _parse_500wan_xml(self, xml_content: str) -> List[Dict]:
        """解析500彩票网XML数据"""
        data = []
        
        try:
            from xml.etree import ElementTree as ET
            
            root = ET.fromstring(xml_content)
            
            for row in root.findall('.//row'):
                try:
                    period = row.get('expect', '')
                    draw_date = row.get('opendate', '')
                    opencode = row.get('opencode', '')
                    
                    if opencode:
                        # 解析开奖号码 (格式: "01,02,03,04,05,06+07")
                        if '+' in opencode:
                            red_part, blue_part = opencode.split('+')
                            red_balls = red_part.split(',')
                            blue_ball = blue_part
                            
                            # 格式化号码
                            red_balls_formatted = [f"{int(ball):02d}" for ball in red_balls if ball.isdigit()]
                            blue_ball_formatted = f"{int(blue_ball):02d}" if blue_ball.isdigit() else blue_ball
                            
                            # 验证数据完整性
                            if period and len(red_balls_formatted) == 6 and blue_ball_formatted:
                                # 只保留2003-2012年的数据
                                if period.startswith(('2003', '2004', '2005', '2006', '2007', '2008', '2009', '2010', '2011', '2012')):
                                    data.append({
                                        'period': period,
                                        'draw_date': draw_date,
                                        'red_balls': red_balls_formatted,
                                        'blue_ball': blue_ball_formatted,
                                        'red_balls_str': ','.join(red_balls_formatted),
                                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                        'data_source': '500wan_early_data'
                                    })
                
                except Exception as e:
                    continue  # 跳过解析失败的行
        
        except Exception as e:
            print(f"❌ 解析500彩票网XML失败: {e}")
        
        return data
    
    def _get_data_from_dayjia(self) -> List[Dict]:
        """从彩票大赢家获取数据"""
        try:
            print("  📡 尝试彩票大赢家...")
            
            # 彩票大赢家的历史数据接口
            base_url = "http://www.dayjia.com/dlt/history/"
            
            early_data = []
            
            # 尝试获取历史数据页面
            for page in range(1, 50):  # 尝试前50页
                try:
                    url = f"{base_url}?page={page}"
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        page_data = self._parse_dayjia_page(soup)
                        
                        if page_data:
                            # 检查是否包含早期数据
                            has_early_data = any(
                                item['period'].startswith(('2003', '2004', '2005', '2006', '2007', '2008', '2009', '2010', '2011', '2012'))
                                for item in page_data
                            )
                            
                            if has_early_data:
                                early_data.extend([
                                    item for item in page_data
                                    if item['period'].startswith(('2003', '2004', '2005', '2006', '2007', '2008', '2009', '2010', '2011', '2012'))
                                ])
                                print(f"    ✅ 第{page}页获取到早期数据")
                            else:
                                # 如果当前页没有早期数据，可能已经超出范围
                                if page > 10:  # 给一些缓冲
                                    break
                        else:
                            break  # 没有数据，停止
                    
                    time.sleep(0.5)  # 避免请求过快
                
                except Exception as e:
                    print(f"    ❌ 第{page}页获取失败: {e}")
                    continue
            
            return early_data
        
        except Exception as e:
            print(f"❌ 彩票大赢家数据获取失败: {e}")
            return []
    
    def _parse_dayjia_page(self, soup: BeautifulSoup) -> List[Dict]:
        """解析彩票大赢家页面"""
        data = []
        
        try:
            # 查找数据表格
            tables = soup.find_all('table', class_='history-table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows[1:]:  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    
                    if len(cells) >= 9:
                        try:
                            period = cells[0].get_text().strip()
                            draw_date = cells[1].get_text().strip()
                            
                            # 提取红球
                            red_balls = []
                            for i in range(2, 8):
                                ball = cells[i].get_text().strip()
                                if ball.isdigit():
                                    red_balls.append(f"{int(ball):02d}")
                            
                            # 提取蓝球
                            blue_ball = cells[8].get_text().strip()
                            if blue_ball.isdigit():
                                blue_ball = f"{int(blue_ball):02d}"
                            
                            if period and len(red_balls) == 6 and blue_ball:
                                data.append({
                                    'period': period,
                                    'draw_date': draw_date,
                                    'red_balls': red_balls,
                                    'blue_ball': blue_ball,
                                    'red_balls_str': ','.join(red_balls),
                                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    'data_source': 'dayjia_early_data'
                                })
                        
                        except Exception as e:
                            continue
        
        except Exception as e:
            print(f"❌ 解析彩票大赢家页面失败: {e}")
        
        return data
    
    def _deduplicate_data(self, data: List[Dict]) -> List[Dict]:
        """去重数据"""
        seen_periods = set()
        unique_data = []
        
        for item in data:
            period = item.get('period', '')
            if period and period not in seen_periods:
                seen_periods.add(period)
                unique_data.append(item)
        
        # 按期号排序
        unique_data.sort(key=lambda x: x.get('period', ''))
        
        return unique_data
    
    def create_sample_early_data(self) -> List[Dict]:
        """创建2003-2012年的示例数据"""
        print("🎲 由于无法获取真实的早期数据，创建示例数据用于演示...")
        
        import random
        
        sample_data = []
        
        # 双色球历史信息
        # 2003年2月16日首次开奖，期号2003008
        # 每年大约150-156期
        
        periods_per_year = {
            2003: 146,  # 2003年从第8期开始
            2004: 154,
            2005: 154,
            2006: 154,
            2007: 154,
            2008: 154,
            2009: 154,
            2010: 154,
            2011: 154,
            2012: 154
        }
        
        for year, total_periods in periods_per_year.items():
            start_period = 8 if year == 2003 else 1
            
            for period_num in range(start_period, start_period + total_periods):
                period = f"{year}{period_num:03d}"
                
                # 生成随机开奖号码
                red_balls = sorted(random.sample(range(1, 34), 6))
                red_balls_str = [f"{ball:02d}" for ball in red_balls]
                blue_ball = f"{random.randint(1, 16):02d}"
                
                # 估算开奖日期
                days_offset = (period_num - start_period) * 2.4  # 大约每2.4天一期
                base_date = date(year, 2, 16) if year == 2003 else date(year, 1, 1)
                
                try:
                    from datetime import timedelta
                    draw_date_obj = base_date + timedelta(days=int(days_offset))
                    draw_date = draw_date_obj.strftime('%Y-%m-%d')
                    
                    # 添加星期信息
                    weekdays = ['一', '二', '三', '四', '五', '六', '日']
                    weekday = weekdays[draw_date_obj.weekday()]
                    draw_date = f"{draw_date}({weekday})"
                    
                except:
                    draw_date = f"{year}-01-01(一)"
                
                sample_data.append({
                    'period': period,
                    'draw_date': draw_date,
                    'red_balls': red_balls_str,
                    'blue_ball': blue_ball,
                    'red_balls_str': ','.join(red_balls_str),
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'data_source': 'sample_early_data'
                })
        
        print(f"✅ 创建了 {len(sample_data)} 期示例早期数据")
        return sample_data


def main():
    """主函数"""
    print("🔍 双色球早期数据获取工具 (2003-2012年)")
    print("=" * 60)
    
    crawler = EarlyDataCrawler()
    
    try:
        # 尝试获取真实的早期数据
        early_data = crawler.get_early_data_from_multiple_sources()
        
        if not early_data:
            print("\n❌ 无法获取真实的早期数据")
            print("🎲 是否创建示例数据用于演示？(y/n): ", end="")
            
            # 自动选择创建示例数据
            choice = 'y'
            print(choice)
            
            if choice.lower() == 'y':
                early_data = crawler.create_sample_early_data()
        
        if early_data:
            # 保存早期数据
            filename = "ssq_early_data_2003_2012.json"
            filepath = os.path.join(crawler.data_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(early_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 早期数据已保存: {filepath}")
            print(f"📊 数据统计:")
            print(f"   总期数: {len(early_data)}")
            print(f"   最早期号: {early_data[0]['period']}")
            print(f"   最晚期号: {early_data[-1]['period']}")
            
            # 按年份统计
            year_stats = {}
            for item in early_data:
                year = item['period'][:4]
                year_stats[year] = year_stats.get(year, 0) + 1
            
            print(f"\n📅 各年期数统计:")
            for year, count in sorted(year_stats.items()):
                print(f"   {year}年: {count} 期")
        
        else:
            print("❌ 未能获取任何早期数据")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
