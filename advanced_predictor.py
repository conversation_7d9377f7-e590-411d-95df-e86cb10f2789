"""
高级双色球预测分析器
包含更多统计维度和预测策略
"""

import json
import random
import math
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import pandas as pd
from data_processor import DataProcessor


class AdvancedLotteryPredictor:
    """高级双色球预测分析器"""
    
    def __init__(self):
        self.processor = DataProcessor()
        self.data = []
        
    def load_data(self) -> bool:
        """加载历史数据"""
        self.data = self.processor.load_from_json()
        if not self.data:
            print("❌ 没有找到历史数据")
            return False
        print(f"✅ 成功加载 {len(self.data)} 期历史数据")
        return True
    
    def analyze_missing_numbers(self) -> Dict:
        """分析遗漏号码"""
        red_missing = {i: 0 for i in range(1, 34)}
        blue_missing = {i: 0 for i in range(1, 17)}
        
        # 分析红球遗漏
        for period_idx, item in enumerate(self.data):
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            
            # 更新遗漏计数
            for ball in range(1, 34):
                if ball in red_balls_int:
                    red_missing[ball] = 0  # 重置遗漏
                else:
                    red_missing[ball] += 1
        
        # 分析蓝球遗漏
        for period_idx, item in enumerate(self.data):
            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                blue_ball_int = int(blue_ball)
                
                for ball in range(1, 17):
                    if ball == blue_ball_int:
                        blue_missing[ball] = 0
                    else:
                        blue_missing[ball] += 1
        
        return {
            'red_missing': red_missing,
            'blue_missing': blue_missing,
            'red_most_missing': sorted(red_missing.items(), key=lambda x: x[1], reverse=True)[:10],
            'blue_most_missing': sorted(blue_missing.items(), key=lambda x: x[1], reverse=True)[:8]
        }
    
    def analyze_number_combinations(self) -> Dict:
        """分析号码组合模式"""
        combinations = {
            'adjacent_pairs': Counter(),  # 相邻号码对
            'sum_analysis': [],           # 和值分析
            'span_analysis': [],          # 跨度分析
            'ac_values': [],              # AC值分析
            'zone_patterns': Counter()    # 区间分布
        }
        
        for item in self.data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = sorted([int(ball) for ball in red_balls if ball.isdigit()])
            
            if len(red_balls_int) == 6:
                # 相邻号码对分析
                for i in range(len(red_balls_int) - 1):
                    if red_balls_int[i+1] - red_balls_int[i] == 1:
                        combinations['adjacent_pairs'][(red_balls_int[i], red_balls_int[i+1])] += 1
                
                # 和值分析
                total_sum = sum(red_balls_int)
                combinations['sum_analysis'].append(total_sum)
                
                # 跨度分析（最大值-最小值）
                span = red_balls_int[-1] - red_balls_int[0]
                combinations['span_analysis'].append(span)
                
                # AC值分析（算术复杂性）
                ac_value = self._calculate_ac_value(red_balls_int)
                combinations['ac_values'].append(ac_value)
                
                # 区间分布 (1-11, 12-22, 23-33)
                zone1 = sum(1 for ball in red_balls_int if 1 <= ball <= 11)
                zone2 = sum(1 for ball in red_balls_int if 12 <= ball <= 22)
                zone3 = sum(1 for ball in red_balls_int if 23 <= ball <= 33)
                combinations['zone_patterns'][(zone1, zone2, zone3)] += 1
        
        return combinations
    
    def _calculate_ac_value(self, numbers: List[int]) -> int:
        """计算AC值（算术复杂性）"""
        if len(numbers) < 2:
            return 0
        
        differences = set()
        for i in range(len(numbers)):
            for j in range(i + 1, len(numbers)):
                differences.add(abs(numbers[i] - numbers[j]))
        
        return len(differences) - len(numbers) + 1
    
    def analyze_periodic_patterns(self) -> Dict:
        """分析周期性模式"""
        # 按星期几分析
        weekday_patterns = defaultdict(list)
        
        for item in self.data:
            date_str = item.get('draw_date', '')
            if '(' in date_str and ')' in date_str:
                weekday = date_str.split('(')[1].split(')')[0]
                
                red_balls = item.get('red_balls', [])
                if isinstance(red_balls, str):
                    red_balls = red_balls.split(',')
                
                red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
                blue_ball = item.get('blue_ball', '')
                
                weekday_patterns[weekday].append({
                    'red_balls': red_balls_int,
                    'blue_ball': int(blue_ball) if blue_ball and blue_ball.isdigit() else 0
                })
        
        return weekday_patterns
    
    def generate_smart_predictions(self, num_predictions: int = 5) -> List[Dict]:
        """生成智能预测"""
        if not self.data:
            return []
        
        missing_analysis = self.analyze_missing_numbers()
        combo_analysis = self.analyze_number_combinations()
        
        predictions = []
        
        for i in range(num_predictions):
            # 策略组合
            if i == 0:
                # 策略1: 遗漏号码 + 热门号码
                pred = self._predict_missing_hot_strategy(missing_analysis)
            elif i == 1:
                # 策略2: 基于和值范围
                pred = self._predict_sum_range_strategy(combo_analysis)
            elif i == 2:
                # 策略3: 区间均衡策略
                pred = self._predict_zone_balance_strategy(combo_analysis)
            elif i == 3:
                # 策略4: AC值优化策略
                pred = self._predict_ac_optimized_strategy(combo_analysis)
            else:
                # 策略5: 综合策略
                pred = self._predict_comprehensive_strategy(missing_analysis, combo_analysis)
            
            predictions.append({
                'strategy': f"策略{i+1}",
                'red_balls': pred['red_balls'],
                'blue_ball': pred['blue_ball'],
                'red_balls_str': ','.join([f"{ball:02d}" for ball in pred['red_balls']]),
                'blue_ball_str': f"{pred['blue_ball']:02d}",
                'analysis': pred.get('analysis', '')
            })
        
        return predictions
    
    def _predict_missing_hot_strategy(self, missing_analysis: Dict) -> Dict:
        """遗漏号码+热门号码策略"""
        # 选择3个遗漏较多的号码
        missing_red = [ball for ball, count in missing_analysis['red_most_missing'][:8]]
        selected_missing = random.sample(missing_red, min(3, len(missing_red)))
        
        # 选择3个最近热门的号码
        recent_data = self.data[:30]  # 最近30期
        recent_red_balls = []
        for item in recent_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            recent_red_balls.extend([int(ball) for ball in red_balls if ball.isdigit()])
        
        hot_counter = Counter(recent_red_balls)
        hot_balls = [ball for ball, _ in hot_counter.most_common(10) if ball not in selected_missing]
        selected_hot = random.sample(hot_balls, min(3, len(hot_balls)))
        
        red_prediction = sorted(selected_missing + selected_hot)
        
        # 蓝球选择遗漏较多的
        missing_blue = [ball for ball, count in missing_analysis['blue_most_missing'][:5]]
        blue_prediction = random.choice(missing_blue) if missing_blue else random.randint(1, 16)
        
        return {
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'analysis': '遗漏号码与热门号码结合'
        }
    
    def _predict_sum_range_strategy(self, combo_analysis: Dict) -> Dict:
        """基于和值范围策略"""
        # 分析最常见的和值范围
        sum_counter = Counter(combo_analysis['sum_analysis'])
        target_sum_range = (90, 120)  # 常见和值范围
        
        red_prediction = []
        attempts = 0
        while len(red_prediction) != 6 and attempts < 100:
            red_prediction = sorted(random.sample(range(1, 34), 6))
            if target_sum_range[0] <= sum(red_prediction) <= target_sum_range[1]:
                break
            attempts += 1
        
        if len(red_prediction) != 6:
            red_prediction = sorted(random.sample(range(1, 34), 6))
        
        blue_prediction = random.randint(1, 16)
        
        return {
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'analysis': f'和值控制在{target_sum_range[0]}-{target_sum_range[1]}范围'
        }
    
    def _predict_zone_balance_strategy(self, combo_analysis: Dict) -> Dict:
        """区间均衡策略"""
        # 最常见的区间分布
        zone_counter = combo_analysis['zone_patterns']
        most_common_pattern = zone_counter.most_common(1)[0][0] if zone_counter else (2, 2, 2)
        
        zone1_count, zone2_count, zone3_count = most_common_pattern
        
        red_prediction = []
        
        # 从各区间选择号码
        zone1_balls = random.sample(range(1, 12), min(zone1_count, 11))
        zone2_balls = random.sample(range(12, 23), min(zone2_count, 11))
        zone3_balls = random.sample(range(23, 34), min(zone3_count, 11))
        
        red_prediction = sorted(zone1_balls + zone2_balls + zone3_balls)
        
        # 确保正好6个球
        while len(red_prediction) < 6:
            red_prediction.append(random.randint(1, 33))
        red_prediction = sorted(list(set(red_prediction)))[:6]
        
        blue_prediction = random.randint(1, 16)
        
        return {
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'analysis': f'区间分布{zone1_count}-{zone2_count}-{zone3_count}'
        }
    
    def _predict_ac_optimized_strategy(self, combo_analysis: Dict) -> Dict:
        """AC值优化策略"""
        # 目标AC值范围（基于历史数据）
        avg_ac = sum(combo_analysis['ac_values']) / len(combo_analysis['ac_values']) if combo_analysis['ac_values'] else 10
        target_ac_range = (int(avg_ac - 2), int(avg_ac + 2))
        
        red_prediction = []
        attempts = 0
        while attempts < 100:
            red_prediction = sorted(random.sample(range(1, 34), 6))
            ac_value = self._calculate_ac_value(red_prediction)
            if target_ac_range[0] <= ac_value <= target_ac_range[1]:
                break
            attempts += 1
        
        if not red_prediction:
            red_prediction = sorted(random.sample(range(1, 34), 6))
        
        blue_prediction = random.randint(1, 16)
        
        return {
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'analysis': f'AC值控制在{target_ac_range[0]}-{target_ac_range[1]}范围'
        }
    
    def _predict_comprehensive_strategy(self, missing_analysis: Dict, combo_analysis: Dict) -> Dict:
        """综合策略"""
        # 综合考虑多个因素
        red_prediction = []
        
        # 1个遗漏号码
        missing_red = [ball for ball, count in missing_analysis['red_most_missing'][:5]]
        if missing_red:
            red_prediction.append(random.choice(missing_red))
        
        # 2个热门号码
        recent_data = self.data[:20]
        recent_red_balls = []
        for item in recent_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            recent_red_balls.extend([int(ball) for ball in red_balls if ball.isdigit()])
        
        hot_counter = Counter(recent_red_balls)
        hot_balls = [ball for ball, _ in hot_counter.most_common(8) if ball not in red_prediction]
        red_prediction.extend(random.sample(hot_balls, min(2, len(hot_balls))))
        
        # 3个随机号码（避免重复）
        remaining_balls = [ball for ball in range(1, 34) if ball not in red_prediction]
        red_prediction.extend(random.sample(remaining_balls, min(3, len(remaining_balls))))
        
        red_prediction = sorted(red_prediction[:6])
        
        # 蓝球综合选择
        blue_missing = [ball for ball, count in missing_analysis['blue_most_missing'][:3]]
        recent_blue_balls = [int(item.get('blue_ball', 0)) for item in self.data[:15] 
                           if item.get('blue_ball', '').isdigit()]
        blue_hot = Counter(recent_blue_balls).most_common(3)
        
        blue_candidates = blue_missing + [ball for ball, _ in blue_hot]
        blue_prediction = random.choice(blue_candidates) if blue_candidates else random.randint(1, 16)
        
        return {
            'red_balls': red_prediction,
            'blue_ball': blue_prediction,
            'analysis': '综合遗漏、热门、随机因素'
        }
    
    def print_advanced_analysis(self):
        """打印高级分析报告"""
        if not self.load_data():
            return
        
        print("=" * 100)
        print("🎯 双色球高级分析与智能预测报告")
        print("=" * 100)
        
        # 遗漏分析
        missing_analysis = self.analyze_missing_numbers()
        print("🔍 遗漏号码分析:")
        print(f"   红球遗漏最多: {[f'{ball}({count}期)' for ball, count in missing_analysis['red_most_missing'][:8]]}")
        print(f"   蓝球遗漏最多: {[f'{ball}({count}期)' for ball, count in missing_analysis['blue_most_missing'][:5]]}")
        
        # 组合分析
        combo_analysis = self.analyze_number_combinations()
        print(f"\n📊 号码组合分析:")
        avg_sum = sum(combo_analysis['sum_analysis']) / len(combo_analysis['sum_analysis'])
        avg_span = sum(combo_analysis['span_analysis']) / len(combo_analysis['span_analysis'])
        avg_ac = sum(combo_analysis['ac_values']) / len(combo_analysis['ac_values'])
        
        print(f"   平均和值: {avg_sum:.1f}")
        print(f"   平均跨度: {avg_span:.1f}")
        print(f"   平均AC值: {avg_ac:.1f}")
        
        most_common_zone = combo_analysis['zone_patterns'].most_common(1)[0]
        print(f"   最常见区间分布: {most_common_zone[0]} (出现{most_common_zone[1]}次)")
        
        # 智能预测
        predictions = self.generate_smart_predictions(5)
        
        print(f"\n🎯 智能预测 (下一期: {self._get_next_period()}):")
        print("=" * 80)
        for pred in predictions:
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = pred['blue_ball_str']
            print(f"   {pred['strategy']}: 🔴 {red_str} | 🔵 {blue_str}")
            print(f"   分析: {pred['analysis']}")
            print()
        
        print("⚠️  重要提醒:")
        print("   • 本预测基于统计学分析，彩票具有随机性")
        print("   • 预测结果仅供参考，不保证中奖")
        print("   • 请理性购彩，量力而行")
        print("=" * 100)
    
    def _get_next_period(self) -> str:
        """获取下一期期号"""
        if not self.data:
            return "未知"
        
        latest_period = self.data[0]['period']
        year = int(latest_period[:4])
        period_num = int(latest_period[4:])
        
        next_period_num = period_num + 1
        if next_period_num > 156:
            year += 1
            next_period_num = 1
        
        return f"{year}{next_period_num:03d}"


def main():
    """主函数"""
    predictor = AdvancedLotteryPredictor()
    predictor.print_advanced_analysis()


if __name__ == "__main__":
    main()
