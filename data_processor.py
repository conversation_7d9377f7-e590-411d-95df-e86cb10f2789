"""
数据处理和存储模块
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict
import config


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.data_dir = config.DATA_DIR
        self._ensure_data_dir()
    
    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def save_to_json(self, data: List[Dict], filename: str = None) -> str:
        """保存数据到JSON文件"""
        if filename is None:
            filename = config.JSON_FILE
        
        filepath = os.path.join(self.data_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {filepath}")
        return filepath
    
    def save_to_csv(self, data: List[Dict], filename: str = None) -> str:
        """保存数据到CSV文件"""
        if filename is None:
            filename = config.CSV_FILE
        
        filepath = os.path.join(self.data_dir, filename)
        
        # 转换为DataFrame
        df = self._convert_to_dataframe(data)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        print(f"数据已保存到: {filepath}")
        return filepath
    
    def save_to_excel(self, data: List[Dict], filename: str = None) -> str:
        """保存数据到Excel文件"""
        if filename is None:
            filename = config.EXCEL_FILE
        
        filepath = os.path.join(self.data_dir, filename)
        
        # 转换为DataFrame
        df = self._convert_to_dataframe(data)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='双色球历史数据', index=False)
            
            # 添加统计信息
            stats_df = self._generate_statistics(data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"数据已保存到: {filepath}")
        return filepath
    
    def _convert_to_dataframe(self, data: List[Dict]) -> pd.DataFrame:
        """转换数据为DataFrame"""
        processed_data = []
        
        for item in data:
            red_balls = item.get('red_balls', [])
            
            # 确保红球数据是列表格式
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            # 补齐红球数据到6个
            while len(red_balls) < 6:
                red_balls.append('')
            
            row = {
                '期号': item.get('period', ''),
                '开奖日期': item.get('draw_date', ''),
                '红球1': red_balls[0] if len(red_balls) > 0 else '',
                '红球2': red_balls[1] if len(red_balls) > 1 else '',
                '红球3': red_balls[2] if len(red_balls) > 2 else '',
                '红球4': red_balls[3] if len(red_balls) > 3 else '',
                '红球5': red_balls[4] if len(red_balls) > 4 else '',
                '红球6': red_balls[5] if len(red_balls) > 5 else '',
                '蓝球': item.get('blue_ball', ''),
                '红球号码': item.get('red_balls_str', ''),
                '数据来源': item.get('data_source', ''),
                '获取时间': item.get('crawl_time', '')
            }
            processed_data.append(row)
        
        return pd.DataFrame(processed_data)
    
    def _generate_statistics(self, data: List[Dict]) -> pd.DataFrame:
        """生成统计信息"""
        stats = [
            {'统计项': '总期数', '数值': len(data)},
            {'统计项': '最新期号', '数值': data[0]['period'] if data else ''},
            {'统计项': '最早期号', '数值': data[-1]['period'] if data else ''},
            {'统计项': '数据来源', '数值': data[0].get('data_source', '') if data else ''},
            {'统计项': '获取时间', '数值': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        ]
        
        # 红球统计
        if data:
            red_balls_all = []
            blue_balls_all = []
            
            for item in data:
                red_balls = item.get('red_balls', [])
                if isinstance(red_balls, str):
                    red_balls = red_balls.split(',')
                
                for ball in red_balls:
                    if ball and ball.isdigit():
                        red_balls_all.append(int(ball))
                
                blue_ball = item.get('blue_ball', '')
                if blue_ball and str(blue_ball).isdigit():
                    blue_balls_all.append(int(blue_ball))
            
            if red_balls_all:
                stats.extend([
                    {'统计项': '红球最小值', '数值': min(red_balls_all)},
                    {'统计项': '红球最大值', '数值': max(red_balls_all)},
                    {'统计项': '红球平均值', '数值': round(sum(red_balls_all) / len(red_balls_all), 2)}
                ])
            
            if blue_balls_all:
                stats.extend([
                    {'统计项': '蓝球最小值', '数值': min(blue_balls_all)},
                    {'统计项': '蓝球最大值', '数值': max(blue_balls_all)},
                    {'统计项': '蓝球平均值', '数值': round(sum(blue_balls_all) / len(blue_balls_all), 2)}
                ])
        
        return pd.DataFrame(stats)
    
    def validate_data(self, data: List[Dict]) -> Dict:
        """验证数据完整性"""
        total_records = len(data)
        valid_records = 0
        invalid_records = 0
        errors = []
        
        for item in data:
            item_errors = []
            
            # 检查期号
            if not item.get('period'):
                item_errors.append('缺少期号')
            
            # 检查红球
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            if len(red_balls) != 6:
                item_errors.append(f'红球数量错误: {len(red_balls)}')
            else:
                for i, ball in enumerate(red_balls):
                    if not ball or not str(ball).isdigit():
                        item_errors.append(f'红球{i+1}格式错误: {ball}')
                    elif not (1 <= int(ball) <= 33):
                        item_errors.append(f'红球{i+1}超出范围: {ball}')
            
            # 检查蓝球
            blue_ball = item.get('blue_ball', '')
            if not blue_ball or not str(blue_ball).isdigit():
                item_errors.append(f'蓝球格式错误: {blue_ball}')
            elif not (1 <= int(blue_ball) <= 16):
                item_errors.append(f'蓝球超出范围: {blue_ball}')
            
            if item_errors:
                invalid_records += 1
                errors.append({
                    'period': item.get('period', 'unknown'),
                    'errors': item_errors
                })
            else:
                valid_records += 1
        
        return {
            'total_records': total_records,
            'valid_records': valid_records,
            'invalid_records': invalid_records,
            'errors': errors
        }
    
    def load_from_json(self, filename: str = None) -> List[Dict]:
        """从JSON文件加载数据"""
        if filename is None:
            filename = config.JSON_FILE
        
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            return []
        
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
