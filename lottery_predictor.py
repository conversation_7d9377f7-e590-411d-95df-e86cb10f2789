"""
双色球号码预测分析模块
基于历史数据进行统计分析和号码预测
"""

import json
import random
from collections import Counter, defaultdict
from datetime import datetime
from typing import List, Dict, Tuple
import pandas as pd
from data_processor import DataProcessor


class LotteryPredictor:
    """双色球号码预测器"""
    
    def __init__(self):
        self.processor = DataProcessor()
        self.data = []
        self.red_balls_history = []
        self.blue_balls_history = []
        
    def load_data(self) -> bool:
        """加载历史数据"""
        self.data = self.processor.load_from_json()
        if not self.data:
            print("❌ 没有找到历史数据，请先运行 main.py 获取数据")
            return False
        
        # 提取红球和蓝球历史数据
        for item in self.data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            self.red_balls_history.extend(red_balls_int)
            
            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                self.blue_balls_history.append(int(blue_ball))
        
        print(f"✅ 成功加载 {len(self.data)} 期历史数据")
        return True
    
    def analyze_frequency(self) -> Dict:
        """分析号码出现频率"""
        red_counter = Counter(self.red_balls_history)
        blue_counter = Counter(self.blue_balls_history)
        
        # 红球频率分析
        red_freq = {}
        total_red = len(self.red_balls_history)
        for ball in range(1, 34):
            count = red_counter.get(ball, 0)
            red_freq[ball] = {
                'count': count,
                'frequency': count / total_red if total_red > 0 else 0,
                'percentage': (count / total_red * 100) if total_red > 0 else 0
            }
        
        # 蓝球频率分析
        blue_freq = {}
        total_blue = len(self.blue_balls_history)
        for ball in range(1, 17):
            count = blue_counter.get(ball, 0)
            blue_freq[ball] = {
                'count': count,
                'frequency': count / total_blue if total_blue > 0 else 0,
                'percentage': (count / total_blue * 100) if total_blue > 0 else 0
            }
        
        return {
            'red_frequency': red_freq,
            'blue_frequency': blue_freq,
            'red_most_common': red_counter.most_common(10),
            'red_least_common': red_counter.most_common()[-10:],
            'blue_most_common': blue_counter.most_common(8),
            'blue_least_common': blue_counter.most_common()[-8:]
        }
    
    def analyze_recent_trends(self, recent_periods: int = 50) -> Dict:
        """分析最近期数的趋势"""
        if len(self.data) < recent_periods:
            recent_periods = len(self.data)
        
        recent_data = self.data[:recent_periods]
        recent_red_balls = []
        recent_blue_balls = []
        
        for item in recent_data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = [int(ball) for ball in red_balls if ball.isdigit()]
            recent_red_balls.extend(red_balls_int)
            
            blue_ball = item.get('blue_ball', '')
            if blue_ball and str(blue_ball).isdigit():
                recent_blue_balls.append(int(blue_ball))
        
        recent_red_counter = Counter(recent_red_balls)
        recent_blue_counter = Counter(recent_blue_balls)
        
        return {
            'recent_periods': recent_periods,
            'red_hot': recent_red_counter.most_common(10),
            'red_cold': recent_red_counter.most_common()[-10:],
            'blue_hot': recent_blue_counter.most_common(5),
            'blue_cold': recent_blue_counter.most_common()[-5:]
        }
    
    def analyze_patterns(self) -> Dict:
        """分析号码模式"""
        patterns = {
            'consecutive_pairs': defaultdict(int),
            'sum_ranges': defaultdict(int),
            'odd_even_patterns': defaultdict(int),
            'zone_distributions': defaultdict(int)
        }
        
        for item in self.data:
            red_balls = item.get('red_balls', [])
            if isinstance(red_balls, str):
                red_balls = red_balls.split(',')
            
            red_balls_int = sorted([int(ball) for ball in red_balls if ball.isdigit()])
            
            if len(red_balls_int) == 6:
                # 连号分析
                consecutive_count = 0
                for i in range(len(red_balls_int) - 1):
                    if red_balls_int[i+1] - red_balls_int[i] == 1:
                        consecutive_count += 1
                patterns['consecutive_pairs'][consecutive_count] += 1
                
                # 和值分析
                total_sum = sum(red_balls_int)
                sum_range = f"{(total_sum // 10) * 10}-{(total_sum // 10) * 10 + 9}"
                patterns['sum_ranges'][sum_range] += 1
                
                # 奇偶分析
                odd_count = sum(1 for ball in red_balls_int if ball % 2 == 1)
                even_count = 6 - odd_count
                patterns['odd_even_patterns'][f"{odd_count}奇{even_count}偶"] += 1
                
                # 区间分析 (1-11, 12-22, 23-33)
                zone1 = sum(1 for ball in red_balls_int if 1 <= ball <= 11)
                zone2 = sum(1 for ball in red_balls_int if 12 <= ball <= 22)
                zone3 = sum(1 for ball in red_balls_int if 23 <= ball <= 33)
                patterns['zone_distributions'][f"{zone1}-{zone2}-{zone3}"] += 1
        
        return patterns
    
    def predict_numbers(self) -> Dict:
        """预测下一期号码"""
        if not self.data:
            return {}
        
        # 获取分析结果
        freq_analysis = self.analyze_frequency()
        trend_analysis = self.analyze_recent_trends()
        pattern_analysis = self.analyze_patterns()
        
        # 预测策略1: 基于频率的加权随机选择
        red_weights = {}
        for ball in range(1, 34):
            freq_weight = freq_analysis['red_frequency'][ball]['frequency']
            # 给最近热门号码额外权重
            recent_weight = 0
            for hot_ball, count in trend_analysis['red_hot']:
                if hot_ball == ball:
                    recent_weight = count / trend_analysis['recent_periods']
                    break
            
            red_weights[ball] = freq_weight + recent_weight * 0.5
        
        # 选择红球（避免全部选择最热门的）
        predicted_red = []
        available_balls = list(range(1, 34))
        
        # 选择2-3个热门球
        hot_balls = [ball for ball, _ in trend_analysis['red_hot'][:8]]
        predicted_red.extend(random.sample(hot_balls, min(3, len(hot_balls))))
        
        # 选择2-3个中等频率球
        medium_balls = [ball for ball in range(1, 34) 
                       if ball not in hot_balls and 
                       freq_analysis['red_frequency'][ball]['percentage'] > 2.5]
        if medium_balls:
            predicted_red.extend(random.sample(medium_balls, min(2, len(medium_balls))))
        
        # 补充到6个球
        while len(predicted_red) < 6:
            remaining_balls = [ball for ball in available_balls if ball not in predicted_red]
            if remaining_balls:
                # 基于权重选择
                weights = [red_weights.get(ball, 0.01) for ball in remaining_balls]
                selected = random.choices(remaining_balls, weights=weights, k=1)[0]
                predicted_red.append(selected)
            else:
                break
        
        predicted_red = sorted(predicted_red[:6])
        
        # 预测蓝球
        blue_weights = {}
        for ball in range(1, 17):
            freq_weight = freq_analysis['blue_frequency'][ball]['frequency']
            recent_weight = 0
            for hot_ball, count in trend_analysis['blue_hot']:
                if hot_ball == ball:
                    recent_weight = count / trend_analysis['recent_periods']
                    break
            blue_weights[ball] = freq_weight + recent_weight * 0.5
        
        # 选择蓝球
        blue_balls = list(range(1, 17))
        blue_weights_list = [blue_weights.get(ball, 0.01) for ball in blue_balls]
        predicted_blue = random.choices(blue_balls, weights=blue_weights_list, k=1)[0]
        
        # 生成多组预测
        predictions = []
        for i in range(5):  # 生成5组预测
            # 每组都有一定的随机性
            pred_red = self._generate_red_prediction(freq_analysis, trend_analysis)
            pred_blue = self._generate_blue_prediction(freq_analysis, trend_analysis)
            
            predictions.append({
                'red_balls': pred_red,
                'blue_ball': pred_blue,
                'red_balls_str': ','.join([f"{ball:02d}" for ball in pred_red]),
                'blue_ball_str': f"{pred_blue:02d}"
            })
        
        return {
            'next_period': self._get_next_period(),
            'predictions': predictions,
            'analysis_summary': {
                'total_periods_analyzed': len(self.data),
                'recent_trend_periods': trend_analysis['recent_periods'],
                'red_hot_numbers': trend_analysis['red_hot'][:5],
                'blue_hot_numbers': trend_analysis['blue_hot'][:3],
                'most_common_sum_range': max(pattern_analysis['sum_ranges'].items(), key=lambda x: x[1]),
                'most_common_odd_even': max(pattern_analysis['odd_even_patterns'].items(), key=lambda x: x[1])
            }
        }
    
    def _generate_red_prediction(self, freq_analysis: Dict, trend_analysis: Dict) -> List[int]:
        """生成红球预测"""
        prediction = []
        
        # 选择1-2个最热门的球
        hot_balls = [ball for ball, _ in trend_analysis['red_hot'][:6]]
        if hot_balls:
            prediction.extend(random.sample(hot_balls, min(2, len(hot_balls))))
        
        # 选择1-2个高频球
        high_freq_balls = [ball for ball in range(1, 34) 
                          if freq_analysis['red_frequency'][ball]['percentage'] > 3.0 
                          and ball not in prediction]
        if high_freq_balls:
            prediction.extend(random.sample(high_freq_balls, min(2, len(high_freq_balls))))
        
        # 选择1-2个中等频率球
        medium_freq_balls = [ball for ball in range(1, 34) 
                           if 2.5 <= freq_analysis['red_frequency'][ball]['percentage'] <= 3.0 
                           and ball not in prediction]
        if medium_freq_balls:
            prediction.extend(random.sample(medium_freq_balls, min(2, len(medium_freq_balls))))
        
        # 补充到6个球
        while len(prediction) < 6:
            remaining_balls = [ball for ball in range(1, 34) if ball not in prediction]
            if remaining_balls:
                prediction.append(random.choice(remaining_balls))
            else:
                break
        
        return sorted(prediction[:6])
    
    def _generate_blue_prediction(self, freq_analysis: Dict, trend_analysis: Dict) -> int:
        """生成蓝球预测"""
        # 70%概率选择热门球，30%概率选择其他球
        if random.random() < 0.7 and trend_analysis['blue_hot']:
            hot_blues = [ball for ball, _ in trend_analysis['blue_hot'][:5]]
            return random.choice(hot_blues)
        else:
            return random.randint(1, 16)
    
    def _get_next_period(self) -> str:
        """获取下一期期号"""
        if not self.data:
            return "未知"
        
        latest_period = self.data[0]['period']
        year = int(latest_period[:4])
        period_num = int(latest_period[4:])
        
        # 简单的期号递增逻辑
        next_period_num = period_num + 1
        if next_period_num > 156:  # 假设每年最多156期
            year += 1
            next_period_num = 1
        
        return f"{year}{next_period_num:03d}"
    
    def print_analysis_report(self):
        """打印详细分析报告"""
        if not self.load_data():
            return
        
        print("=" * 80)
        print("🎯 双色球号码分析与预测报告")
        print("=" * 80)
        
        # 基础统计
        freq_analysis = self.analyze_frequency()
        trend_analysis = self.analyze_recent_trends()
        pattern_analysis = self.analyze_patterns()
        
        print(f"📊 数据概况:")
        print(f"   分析期数: {len(self.data)} 期")
        print(f"   时间范围: {self.data[-1]['draw_date']} 到 {self.data[0]['draw_date']}")
        print(f"   最新期号: {self.data[0]['period']}")
        
        print(f"\n🔴 红球频率分析 (前10名):")
        for i, (ball, count) in enumerate(freq_analysis['red_most_common'], 1):
            percentage = freq_analysis['red_frequency'][ball]['percentage']
            print(f"   {i:2d}. 号码 {ball:2d}: 出现 {count:3d} 次 ({percentage:.1f}%)")
        
        print(f"\n🔵 蓝球频率分析 (前8名):")
        for i, (ball, count) in enumerate(freq_analysis['blue_most_common'], 1):
            percentage = freq_analysis['blue_frequency'][ball]['percentage']
            print(f"   {i:2d}. 号码 {ball:2d}: 出现 {count:3d} 次 ({percentage:.1f}%)")
        
        print(f"\n🔥 最近{trend_analysis['recent_periods']}期热门号码:")
        print(f"   红球热门: {[f'{ball}({count})' for ball, count in trend_analysis['red_hot'][:8]]}")
        print(f"   蓝球热门: {[f'{ball}({count})' for ball, count in trend_analysis['blue_hot'][:5]]}")
        
        print(f"\n❄️ 最近{trend_analysis['recent_periods']}期冷门号码:")
        print(f"   红球冷门: {[f'{ball}({count})' for ball, count in trend_analysis['red_cold'][:8]]}")
        print(f"   蓝球冷门: {[f'{ball}({count})' for ball, count in trend_analysis['blue_cold'][:5]]}")
        
        # 生成预测
        predictions = self.predict_numbers()
        
        print(f"\n🎯 下一期预测 ({predictions['next_period']}):")
        print("=" * 50)
        for i, pred in enumerate(predictions['predictions'], 1):
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = f"{pred['blue_ball']:02d}"
            print(f"   预测{i}: 🔴 {red_str} | 🔵 {blue_str}")
        
        print(f"\n📈 分析要点:")
        summary = predictions['analysis_summary']
        print(f"   • 最热红球: {[f'{ball}({count})' for ball, count in summary['red_hot_numbers']]}")
        print(f"   • 最热蓝球: {[f'{ball}({count})' for ball, count in summary['blue_hot_numbers']]}")
        print(f"   • 常见和值范围: {summary['most_common_sum_range'][0]} (出现{summary['most_common_sum_range'][1]}次)")
        print(f"   • 常见奇偶组合: {summary['most_common_odd_even'][0]} (出现{summary['most_common_odd_even'][1]}次)")
        
        print(f"\n⚠️  免责声明:")
        print(f"   本预测仅基于历史数据统计分析，彩票开奖具有随机性")
        print(f"   预测结果仅供参考，不构成购彩建议，请理性购彩")
        
        print("=" * 80)


def main():
    """主函数"""
    predictor = LotteryPredictor()
    predictor.print_analysis_report()


if __name__ == "__main__":
    main()
