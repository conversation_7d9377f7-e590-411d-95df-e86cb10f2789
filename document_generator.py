"""
双色球分析结果文档生成器
将所有分析结果生成为详细的文档文件
"""

import os
import json
from datetime import datetime
from collections import Counter
from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
from data_processor import DataProcessor
import config


class DocumentGenerator:
    """文档生成器"""

    def __init__(self):
        self.basic_predictor = LotteryPredictor()
        self.advanced_predictor = AdvancedLotteryPredictor()
        self.processor = DataProcessor()
        self.output_dir = "reports"

        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def generate_all_documents(self):
        """生成所有文档"""
        print("📄 开始生成分析文档...")

        # 加载数据
        if not self.basic_predictor.load_data():
            print("❌ 无法加载数据，文档生成失败")
            return

        self.advanced_predictor.load_data()

        # 生成各种格式的文档
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 1. 生成Markdown报告
        md_file = self.generate_markdown_report(timestamp)

        # 2. 生成HTML报告
        html_file = self.generate_html_report(timestamp)

        # 3. 生成JSON数据报告
        json_file = self.generate_json_report(timestamp)

        # 4. 生成Excel详细报告
        excel_file = self.generate_excel_report(timestamp)

        # 5. 生成纯文本报告
        txt_file = self.generate_text_report(timestamp)

        print("\n✅ 文档生成完成！")
        print("📁 生成的文件:")
        for file_path in [md_file, html_file, json_file, excel_file, txt_file]:
            if file_path:
                print(f"   📄 {file_path}")

    def generate_markdown_report(self, timestamp: str) -> str:
        """生成Markdown格式报告"""
        filename = f"双色球分析报告_{timestamp}.md"
        filepath = os.path.join(self.output_dir, filename)

        data = self.basic_predictor.data
        freq_analysis = self.basic_predictor.analyze_frequency()
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        basic_predictions = self.basic_predictor.predict_numbers()
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(5)

        content = f"""# 双色球综合分析报告

## 📊 报告信息
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据来源**: 中国福利彩票官方API
- **分析期数**: {len(data)} 期
- **数据范围**: {data[-1]['draw_date']} 至 {data[0]['draw_date']}
- **最新期号**: {data[0]['period']}

## 🎯 预测期号
**下一期预测**: {self._get_next_period(data)}

---

## 📈 历史频率统计

### 🔴 红球出现频率 TOP15
| 排名 | 号码 | 出现次数 | 出现频率 |
|------|------|----------|----------|
"""

        for i, (ball, count) in enumerate(freq_analysis['red_most_common'][:15], 1):
            percentage = freq_analysis['red_frequency'][ball]['percentage']
            content += f"| {i} | {ball:02d} | {count} | {percentage:.2f}% |\n"

        content += f"""
### 🔵 蓝球出现频率 TOP10
| 排名 | 号码 | 出现次数 | 出现频率 |
|------|------|----------|----------|
"""

        for i, (ball, count) in enumerate(freq_analysis['blue_most_common'][:10], 1):
            percentage = freq_analysis['blue_frequency'][ball]['percentage']
            content += f"| {i} | {ball:02d} | {count} | {percentage:.2f}% |\n"

        # 最近趋势
        content += f"""
---

## 🔥 最近30期趋势分析

### 热门号码
- **红球热门**: {[f'{ball}({count})' for ball, count in trend_analysis['red_hot'][:10]]}
- **蓝球热门**: {[f'{ball}({count})' for ball, count in trend_analysis['blue_hot'][:5]]}

### 冷门号码
- **红球冷门**: {[f'{ball}({count})' for ball, count in trend_analysis['red_cold'][:10]]}
- **蓝球冷门**: {[f'{ball}({count})' for ball, count in trend_analysis['blue_cold'][:5]]}

---

## 📉 当前遗漏分析

### 遗漏期数最多的号码
- **红球遗漏**: {[f'{ball}({count}期)' for ball, count in missing_analysis['red_most_missing'][:10]]}
- **蓝球遗漏**: {[f'{ball}({count}期)' for ball, count in missing_analysis['blue_most_missing'][:8]]}

---

## 📊 号码组合特征分析

"""

        avg_sum = sum(combo_analysis['sum_analysis']) / len(combo_analysis['sum_analysis'])
        avg_span = sum(combo_analysis['span_analysis']) / len(combo_analysis['span_analysis'])
        avg_ac = sum(combo_analysis['ac_values']) / len(combo_analysis['ac_values'])
        most_common_zone = combo_analysis['zone_patterns'].most_common(1)[0]

        content += f"""- **历史平均和值**: {avg_sum:.1f}
- **历史平均跨度**: {avg_span:.1f}
- **历史平均AC值**: {avg_ac:.1f}
- **最常见区间分布**: {most_common_zone[0]} (出现{most_common_zone[1]}次)

---

## 🎯 预测方案

### 基础统计预测
"""

        for i, pred in enumerate(basic_predictions['predictions'][:3], 1):
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = pred['blue_ball_str']
            content += f"**方案{i}**: 🔴 {red_str} | 🔵 {blue_str}\n\n"

        content += "### 智能策略预测\n"

        for pred in advanced_predictions:
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = pred['blue_ball_str']
            content += f"**{pred['strategy']}**: 🔴 {red_str} | 🔵 {blue_str}\n"
            content += f"- *策略说明*: {pred['analysis']}\n\n"

        # 推荐号码
        all_red_predictions = []
        for pred in basic_predictions['predictions']:
            all_red_predictions.extend(pred['red_balls'])
        for pred in advanced_predictions:
            all_red_predictions.extend(pred['red_balls'])

        red_recommendation = Counter(all_red_predictions).most_common(12)

        content += f"""---

## ⭐ 重点推荐号码

### 🔴 推荐红球 (按预测频次排序)
- **重点关注**: {[f'{ball}({count})' for ball, count in red_recommendation[:6]]}
- **次要关注**: {[f'{ball}({count})' for ball, count in red_recommendation[6:12]]}

### 💡 投注建议

#### 号码选择策略
- **热门红球**: {[ball for ball, _ in trend_analysis['red_hot'][:5]]} (最近30期出现频繁)
- **遗漏红球**: {[ball for ball, _ in missing_analysis['red_most_missing'][:5]]} (当前遗漏期数较多)
- **建议组合**: 2-3个热门号码 + 2-3个遗漏号码

#### 技术指标建议
- **和值范围**: {int(avg_sum-10)} - {int(avg_sum+10)} (历史平均{avg_sum:.0f})
- **跨度范围**: {int(avg_span-5)} - {int(avg_span+5)} (历史平均{avg_span:.0f})
- **区间分布**: 建议采用2-2-2或2-3-1分布
- **奇偶比例**: 建议3:3或4:2

---

## ⚠️ 免责声明

> **重要提醒**:
> - 本预测基于历史数据统计分析，采用多种数学模型
> - 彩票开奖具有随机性，任何预测都不能保证准确性
> - 预测结果仅供参考，不构成投注建议
> - 请理性购彩，量力而行，切勿沉迷
> - 购彩有风险，投注需谨慎

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*数据来源: 中国福利彩票官方API*
*分析工具: 双色球智能预测系统*
"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ Markdown报告已生成: {filepath}")
        return filepath

    def generate_html_report(self, timestamp: str) -> str:
        """生成HTML格式报告"""
        filename = f"双色球分析报告_{timestamp}.html"
        filepath = os.path.join(self.output_dir, filename)

        data = self.basic_predictor.data
        freq_analysis = self.basic_predictor.analyze_frequency()
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        basic_predictions = self.basic_predictor.predict_numbers()
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(5)

        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双色球综合分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #d32f2f;
            text-align: center;
            border-bottom: 3px solid #d32f2f;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #1976d2;
            border-left: 4px solid #1976d2;
            padding-left: 15px;
        }}
        h3 {{
            color: #388e3c;
        }}
        .info-box {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
        .prediction-box {{
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
        }}
        .red-ball {{
            background: #f44336;
            color: white;
            padding: 5px 10px;
            border-radius: 50%;
            margin: 2px;
            display: inline-block;
            min-width: 25px;
            text-align: center;
        }}
        .blue-ball {{
            background: #2196f3;
            color: white;
            padding: 5px 10px;
            border-radius: 50%;
            margin: 2px;
            display: inline-block;
            min-width: 25px;
            text-align: center;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        .warning {{
            background: #ffebee;
            border: 1px solid #f44336;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .highlight {{
            background: #fff9c4;
            padding: 2px 5px;
            border-radius: 3px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 双色球综合分析报告</h1>

        <div class="info-box">
            <h3>📊 报告信息</h3>
            <p><strong>生成时间</strong>: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p><strong>数据来源</strong>: 中国福利彩票官方API</p>
            <p><strong>分析期数</strong>: {len(data)} 期</p>
            <p><strong>数据范围</strong>: {data[-1]['draw_date']} 至 {data[0]['draw_date']}</p>
            <p><strong>最新期号</strong>: {data[0]['period']}</p>
            <p><strong>预测期号</strong>: <span class="highlight">{self._get_next_period(data)}</span></p>
        </div>
"""

        # 添加预测方案
        html_content += """
        <h2>🎯 预测方案</h2>
        <h3>基础统计预测</h3>
"""

        for i, pred in enumerate(basic_predictions['predictions'][:3], 1):
            red_balls_html = ''.join([f'<span class="red-ball">{ball:02d}</span>' for ball in pred['red_balls']])
            blue_ball_html = f'<span class="blue-ball">{pred["blue_ball"]:02d}</span>'
            html_content += f"""
        <div class="prediction-box">
            <strong>方案{i}</strong>: {red_balls_html} | {blue_ball_html}
        </div>
"""

        html_content += "<h3>智能策略预测</h3>"

        for pred in advanced_predictions:
            red_balls_html = ''.join([f'<span class="red-ball">{ball:02d}</span>' for ball in pred['red_balls']])
            blue_ball_html = f'<span class="blue-ball">{pred["blue_ball"]:02d}</span>'
            html_content += f"""
        <div class="prediction-box">
            <strong>{pred['strategy']}</strong>: {red_balls_html} | {blue_ball_html}
            <br><em>策略说明: {pred['analysis']}</em>
        </div>
"""

        # 添加频率统计表
        html_content += """
        <h2>📈 历史频率统计</h2>
        <h3>🔴 红球出现频率 TOP15</h3>
        <table>
            <tr><th>排名</th><th>号码</th><th>出现次数</th><th>出现频率</th></tr>
"""

        for i, (ball, count) in enumerate(freq_analysis['red_most_common'][:15], 1):
            percentage = freq_analysis['red_frequency'][ball]['percentage']
            html_content += f"<tr><td>{i}</td><td><span class='red-ball'>{ball:02d}</span></td><td>{count}</td><td>{percentage:.2f}%</td></tr>\n"

        html_content += """
        </table>

        <h3>🔵 蓝球出现频率 TOP10</h3>
        <table>
            <tr><th>排名</th><th>号码</th><th>出现次数</th><th>出现频率</th></tr>
"""

        for i, (ball, count) in enumerate(freq_analysis['blue_most_common'][:10], 1):
            percentage = freq_analysis['blue_frequency'][ball]['percentage']
            html_content += f"<tr><td>{i}</td><td><span class='blue-ball'>{ball:02d}</span></td><td>{count}</td><td>{percentage:.2f}%</td></tr>\n"

        html_content += "</table>"

        # 添加免责声明
        html_content += """
        <div class="warning">
            <h3>⚠️ 免责声明</h3>
            <ul>
                <li>本预测基于历史数据统计分析，采用多种数学模型</li>
                <li>彩票开奖具有随机性，任何预测都不能保证准确性</li>
                <li>预测结果仅供参考，不构成投注建议</li>
                <li>请理性购彩，量力而行，切勿沉迷</li>
                <li>购彩有风险，投注需谨慎</li>
            </ul>
        </div>

        <hr>
        <p style="text-align: center; color: #666;">
            报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} |
            数据来源: 中国福利彩票官方API |
            分析工具: 双色球智能预测系统
        </p>
    </div>
</body>
</html>"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ HTML报告已生成: {filepath}")
        return filepath

    def _get_next_period(self, data):
        """获取下一期期号"""
        if not data:
            return "未知"

        latest_period = data[0]['period']
        year = int(latest_period[:4])
        period_num = int(latest_period[4:])

        next_period_num = period_num + 1
        if next_period_num > 156:
            year += 1
            next_period_num = 1

        return f"{year}{next_period_num:03d}"

    def generate_json_report(self, timestamp: str) -> str:
        """生成JSON格式报告"""
        filename = f"双色球分析数据_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)

        data = self.basic_predictor.data
        freq_analysis = self.basic_predictor.analyze_frequency()
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        basic_predictions = self.basic_predictor.predict_numbers()
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(5)

        # 构建JSON数据
        report_data = {
            "report_info": {
                "generate_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "data_source": "中国福利彩票官方API",
                "total_periods": len(data),
                "data_range": {
                    "start": data[-1]['draw_date'],
                    "end": data[0]['draw_date']
                },
                "latest_period": data[0]['period'],
                "next_period": self._get_next_period(data)
            },
            "frequency_analysis": {
                "red_balls": {
                    str(ball): {
                        "count": freq_analysis['red_frequency'][ball]['count'],
                        "frequency": freq_analysis['red_frequency'][ball]['frequency'],
                        "percentage": freq_analysis['red_frequency'][ball]['percentage']
                    } for ball in range(1, 34)
                },
                "blue_balls": {
                    str(ball): {
                        "count": freq_analysis['blue_frequency'][ball]['count'],
                        "frequency": freq_analysis['blue_frequency'][ball]['frequency'],
                        "percentage": freq_analysis['blue_frequency'][ball]['percentage']
                    } for ball in range(1, 17)
                },
                "red_top10": [{"ball": ball, "count": count} for ball, count in freq_analysis['red_most_common'][:10]],
                "blue_top8": [{"ball": ball, "count": count} for ball, count in freq_analysis['blue_most_common'][:8]]
            },
            "trend_analysis": {
                "recent_periods": 30,
                "red_hot": [{"ball": ball, "count": count} for ball, count in trend_analysis['red_hot'][:10]],
                "red_cold": [{"ball": ball, "count": count} for ball, count in trend_analysis['red_cold'][:10]],
                "blue_hot": [{"ball": ball, "count": count} for ball, count in trend_analysis['blue_hot'][:5]],
                "blue_cold": [{"ball": ball, "count": count} for ball, count in trend_analysis['blue_cold'][:5]]
            },
            "missing_analysis": {
                "red_missing": missing_analysis['red_missing'],
                "blue_missing": missing_analysis['blue_missing'],
                "red_most_missing": [{"ball": ball, "periods": count} for ball, count in missing_analysis['red_most_missing'][:10]],
                "blue_most_missing": [{"ball": ball, "periods": count} for ball, count in missing_analysis['blue_most_missing'][:8]]
            },
            "combination_analysis": {
                "average_sum": sum(combo_analysis['sum_analysis']) / len(combo_analysis['sum_analysis']),
                "average_span": sum(combo_analysis['span_analysis']) / len(combo_analysis['span_analysis']),
                "average_ac": sum(combo_analysis['ac_values']) / len(combo_analysis['ac_values']),
                "most_common_zone": combo_analysis['zone_patterns'].most_common(1)[0] if combo_analysis['zone_patterns'] else None
            },
            "predictions": {
                "basic_predictions": [
                    {
                        "method": f"基础预测{i+1}",
                        "red_balls": pred['red_balls'],
                        "blue_ball": pred['blue_ball'],
                        "red_balls_str": pred['red_balls_str'],
                        "blue_ball_str": pred['blue_ball_str']
                    } for i, pred in enumerate(basic_predictions['predictions'][:3])
                ],
                "advanced_predictions": [
                    {
                        "strategy": pred['strategy'],
                        "red_balls": pred['red_balls'],
                        "blue_ball": pred['blue_ball'],
                        "red_balls_str": pred['red_balls_str'],
                        "blue_ball_str": pred['blue_ball_str'],
                        "analysis": pred['analysis']
                    } for pred in advanced_predictions
                ]
            }
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print(f"✅ JSON报告已生成: {filepath}")
        return filepath

    def generate_excel_report(self, timestamp: str) -> str:
        """生成Excel格式报告"""
        filename = f"双色球分析报告_{timestamp}.xlsx"
        filepath = os.path.join(self.output_dir, filename)

        data = self.basic_predictor.data
        freq_analysis = self.basic_predictor.analyze_frequency()
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        basic_predictions = self.basic_predictor.predict_numbers()
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(5)

        import pandas as pd

        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 1. 报告概要
            summary_data = [
                ['生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['数据来源', '中国福利彩票官方API'],
                ['分析期数', len(data)],
                ['数据开始', data[-1]['draw_date']],
                ['数据结束', data[0]['draw_date']],
                ['最新期号', data[0]['period']],
                ['预测期号', self._get_next_period(data)]
            ]
            summary_df = pd.DataFrame(summary_data, columns=['项目', '内容'])
            summary_df.to_excel(writer, sheet_name='报告概要', index=False)

            # 2. 红球频率统计
            red_freq_data = []
            for ball in range(1, 34):
                red_freq_data.append([
                    ball,
                    freq_analysis['red_frequency'][ball]['count'],
                    f"{freq_analysis['red_frequency'][ball]['percentage']:.2f}%"
                ])
            red_freq_df = pd.DataFrame(red_freq_data, columns=['号码', '出现次数', '出现频率'])
            red_freq_df = red_freq_df.sort_values('出现次数', ascending=False)
            red_freq_df.to_excel(writer, sheet_name='红球频率统计', index=False)

            # 3. 蓝球频率统计
            blue_freq_data = []
            for ball in range(1, 17):
                blue_freq_data.append([
                    ball,
                    freq_analysis['blue_frequency'][ball]['count'],
                    f"{freq_analysis['blue_frequency'][ball]['percentage']:.2f}%"
                ])
            blue_freq_df = pd.DataFrame(blue_freq_data, columns=['号码', '出现次数', '出现频率'])
            blue_freq_df = blue_freq_df.sort_values('出现次数', ascending=False)
            blue_freq_df.to_excel(writer, sheet_name='蓝球频率统计', index=False)

            # 4. 预测方案
            prediction_data = []

            # 基础预测
            for i, pred in enumerate(basic_predictions['predictions'][:3], 1):
                red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
                prediction_data.append([
                    f'基础预测{i}',
                    red_str,
                    f"{pred['blue_ball']:02d}",
                    '基于频率和趋势分析'
                ])

            # 高级预测
            for pred in advanced_predictions:
                red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
                prediction_data.append([
                    pred['strategy'],
                    red_str,
                    f"{pred['blue_ball']:02d}",
                    pred['analysis']
                ])

            prediction_df = pd.DataFrame(prediction_data, columns=['预测方案', '红球号码', '蓝球号码', '策略说明'])
            prediction_df.to_excel(writer, sheet_name='预测方案', index=False)

            # 5. 遗漏分析
            missing_data = []
            for ball, periods in missing_analysis['red_most_missing'][:20]:
                missing_data.append(['红球', ball, periods])
            for ball, periods in missing_analysis['blue_most_missing'][:10]:
                missing_data.append(['蓝球', ball, periods])

            missing_df = pd.DataFrame(missing_data, columns=['球类', '号码', '遗漏期数'])
            missing_df.to_excel(writer, sheet_name='遗漏分析', index=False)

        print(f"✅ Excel报告已生成: {filepath}")
        return filepath

    def generate_text_report(self, timestamp: str) -> str:
        """生成纯文本报告"""
        filename = f"双色球分析报告_{timestamp}.txt"
        filepath = os.path.join(self.output_dir, filename)

        data = self.basic_predictor.data
        freq_analysis = self.basic_predictor.analyze_frequency()
        trend_analysis = self.basic_predictor.analyze_recent_trends(30)
        missing_analysis = self.advanced_predictor.analyze_missing_numbers()
        combo_analysis = self.advanced_predictor.analyze_number_combinations()
        basic_predictions = self.basic_predictor.predict_numbers()
        advanced_predictions = self.advanced_predictor.generate_smart_predictions(5)

        content = f"""
================================================================================
                           双色球综合分析报告
================================================================================

报告信息:
  生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
  数据来源: 中国福利彩票官方API
  分析期数: {len(data)} 期
  数据范围: {data[-1]['draw_date']} 至 {data[0]['draw_date']}
  最新期号: {data[0]['period']}
  预测期号: {self._get_next_period(data)}

================================================================================
                              预测方案
================================================================================

基础统计预测:
"""

        for i, pred in enumerate(basic_predictions['predictions'][:3], 1):
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = f"{pred['blue_ball']:02d}"
            content += f"  方案{i}: 红球 {red_str} | 蓝球 {blue_str}\n"

        content += "\n智能策略预测:\n"

        for pred in advanced_predictions:
            red_str = ' '.join([f"{ball:02d}" for ball in pred['red_balls']])
            blue_str = f"{pred['blue_ball']:02d}"
            content += f"  {pred['strategy']}: 红球 {red_str} | 蓝球 {blue_str}\n"
            content += f"  策略说明: {pred['analysis']}\n\n"

        content += f"""
================================================================================
                            历史频率统计
================================================================================

红球出现频率 TOP15:
"""

        for i, (ball, count) in enumerate(freq_analysis['red_most_common'][:15], 1):
            percentage = freq_analysis['red_frequency'][ball]['percentage']
            content += f"  {i:2d}. 号码{ball:2d}: {count:3d}次 ({percentage:.2f}%)\n"

        content += "\n蓝球出现频率 TOP10:\n"

        for i, (ball, count) in enumerate(freq_analysis['blue_most_common'][:10], 1):
            percentage = freq_analysis['blue_frequency'][ball]['percentage']
            content += f"  {i:2d}. 号码{ball:2d}: {count:3d}次 ({percentage:.2f}%)\n"

        content += f"""
================================================================================
                            趋势和遗漏分析
================================================================================

最近30期热门号码:
  红球热门: {[f'{ball}({count})' for ball, count in trend_analysis['red_hot'][:10]]}
  蓝球热门: {[f'{ball}({count})' for ball, count in trend_analysis['blue_hot'][:5]]}

最近30期冷门号码:
  红球冷门: {[f'{ball}({count})' for ball, count in trend_analysis['red_cold'][:10]]}
  蓝球冷门: {[f'{ball}({count})' for ball, count in trend_analysis['blue_cold'][:5]]}

当前遗漏分析:
  红球遗漏: {[f'{ball}({count}期)' for ball, count in missing_analysis['red_most_missing'][:10]]}
  蓝球遗漏: {[f'{ball}({count}期)' for ball, count in missing_analysis['blue_most_missing'][:8]]}

================================================================================
                              免责声明
================================================================================

重要提醒:
  • 本预测基于历史数据统计分析，采用多种数学模型
  • 彩票开奖具有随机性，任何预测都不能保证准确性
  • 预测结果仅供参考，不构成投注建议
  • 请理性购彩，量力而行，切勿沉迷
  • 购彩有风险，投注需谨慎

================================================================================
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
数据来源: 中国福利彩票官方API
分析工具: 双色球智能预测系统
================================================================================
"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ 文本报告已生成: {filepath}")
        return filepath


def main():
    """主函数"""
    generator = DocumentGenerator()
    generator.generate_all_documents()


if __name__ == "__main__":
    main()
